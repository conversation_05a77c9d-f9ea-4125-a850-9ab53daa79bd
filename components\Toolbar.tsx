
import React, { useState, useRef } from 'react';
import { type TokenState, type Tool, type TextPreset, UnitType as EUnitType, ArtilleryType, type CustomUnit } from '../types';
import { UNIT_TYPES, FONT_FACES, ARTILLERY_TYPES } from '../constants';
import { 
    SwordIcon, ShieldIcon, PikeIcon, ArcherIcon, CavalryIcon, TextIcon, 
    CannonIcon, MetrallaIcon, MortarIcon, BallistaIcon, RocketLauncherIcon, SiegeTowerIcon, TrebuchetIcon, CulverinIcon
} from './icons/UnitIcons';
import { 
    SelectIcon, MoveIcon, CloneIcon, ZoomIcon, ArrowIcon, CircleIcon, PathIcon, TrashIcon, UploadIcon, EraserIcon, UndoIcon, RedoIcon, 
    BookmarkSquareIcon, ChevronDownIcon, PlusIcon, MinusIcon, ExportIcon, ImportIcon, EnlargeIcon
} from './icons/ToolIcons';

interface NewTextState {
    content: string;
    size: number;
    fontFamily: string;
}

interface ToolbarProps {
  activeTool: Tool;
  onToolSelect: (tool: Tool) => void;
  onMapUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onClearAll: () => void;
  selectedColor: string;
  onColorChange: (color: string) => void;
  paletteColors: string[];
  onPaletteColorChange: (index: number, newColor: string) => void;
  onUndo: () => void;
  onRedo: () => void;
  newText: NewTextState;
  onNewTextChange: (state: NewTextState) => void;
  textPresets: TextPreset[];
  onAddTextPreset: () => void;
  onDeleteTextPreset: (id: number) => void;
  onLoadTextPreset: (preset: TextPreset) => void;
  selectedToken: TokenState | null;
  onTokenUpdate: (tokenId: number, updates: Partial<TokenState>) => void;
  onClearPath: (tokenId: number) => void;
  onExportProject: () => void;
  onImportProject: (event: React.ChangeEvent<HTMLInputElement>) => void;
  customUnits: CustomUnit[];
  onCustomUnitUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  customArtillery: CustomUnit[];
  onCustomArtilleryUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  globalTokenSize: number;
  onGlobalTokenSizeChange: (size: number) => void;
}

const unitIcons: Record<string, React.ReactNode> = {
  [EUnitType.Sword]: <SwordIcon />,
  [EUnitType.Shield]: <ShieldIcon />,
  [EUnitType.Pike]: <PikeIcon />,
  [EUnitType.Archer]: <ArcherIcon />,
  [EUnitType.Cavalry]: <CavalryIcon />,
};

const artilleryIcons: Record<ArtilleryType, React.ReactNode> = {
  [ArtilleryType.Cannon]: <CannonIcon />,
  [ArtilleryType.Metralla]: <MetrallaIcon />,
  [ArtilleryType.Mortar]: <MortarIcon />,
  [ArtilleryType.Ballista]: <BallistaIcon />,
  [ArtilleryType.RocketLauncher]: <RocketLauncherIcon />,
  [ArtilleryType.SiegeTower]: <SiegeTowerIcon />,
  [ArtilleryType.Trebuchet]: <TrebuchetIcon />,
  [ArtilleryType.Culverin]: <CulverinIcon />,
};

const ToolButton: React.FC<{
  label: string;
  isActive: boolean;
  onClick: () => void;
  children: React.ReactNode;
}> = ({ label, isActive, onClick, children }) => (
  <button
    onClick={onClick}
    title={label}
    className={`flex items-center justify-start w-full p-3 my-1 rounded-lg transition-colors duration-200 ${
      isActive ? 'bg-blue-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
    }`}
  >
    <div className="w-6 h-6 mr-3">{children}</div>
    <span className="font-medium">{label}</span>
  </button>
);

export const Toolbar: React.FC<ToolbarProps> = ({
  activeTool,
  onToolSelect,
  onMapUpload,
  onClearAll,
  selectedColor,
  onColorChange,
  paletteColors,
  onPaletteColorChange,
  onUndo,
  onRedo,
  newText,
  onNewTextChange,
  textPresets,
  onAddTextPreset,
  onDeleteTextPreset,
  onLoadTextPreset,
  selectedToken,
  onTokenUpdate,
  onClearPath,
  onExportProject,
  onImportProject,
  customUnits,
  onCustomUnitUpload,
  customArtillery,
  onCustomArtilleryUpload,
  globalTokenSize,
  onGlobalTokenSizeChange,
}) => {
  const [isPresetListOpen, setIsPresetListOpen] = useState(false);
  const colorInputRef = useRef<HTMLInputElement>(null);
  const editingColorIndexRef = useRef<number | null>(null);
  const [pendingColorChange, setPendingColorChange] = useState<{ index: number; color: string } | null>(null);

  const handleUnitDragStart = (e: React.DragEvent, type: EUnitType) => {
    const data = JSON.stringify({ type });
    e.dataTransfer.setData('application/json', data);
  };

  const handleCustomUnitDragStart = (e: React.DragEvent, unit: CustomUnit) => {
    const data = JSON.stringify({ type: EUnitType.Custom, imageData: unit.imageData, name: unit.name });
    e.dataTransfer.setData('application/json', data);
  };

  const handleArtilleryDragStart = (e: React.DragEvent, artilleryType: ArtilleryType) => {
    const data = JSON.stringify({ type: EUnitType.Artillery, artilleryType });
    e.dataTransfer.setData('application/json', data);
  };

  const handleCustomArtilleryDragStart = (e: React.DragEvent, unit: CustomUnit) => {
    const data = JSON.stringify({ type: EUnitType.CustomArtillery, imageData: unit.imageData, name: unit.name });
    e.dataTransfer.setData('application/json', data);
  };

  const handleTextDragStart = (e: React.DragEvent) => {
    const data = JSON.stringify({
      isTextToken: true,
      content: newText.content,
      size: newText.size,
      fontFamily: newText.fontFamily,
    });
    e.dataTransfer.setData('application/json', data);
  };

  const handleTextSizeChange = (amount: number) => {
    const newSize = Math.max(0.5, Math.min(5, newText.size + amount));
    onNewTextChange({ ...newText, size: parseFloat(newSize.toFixed(2)) });
  };
  
  const handleColorDoubleClick = (index: number) => {
    if (pendingColorChange) return;
    editingColorIndexRef.current = index;
    colorInputRef.current?.click();
  };

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (editingColorIndexRef.current !== null) {
      setPendingColorChange({ index: editingColorIndexRef.current, color: e.target.value });
    }
  };

  const handleConfirmColorChange = () => {
    if (pendingColorChange) {
      onPaletteColorChange(pendingColorChange.index, pendingColorChange.color);
      setPendingColorChange(null);
      editingColorIndexRef.current = null;
    }
  };

  const handleCancelColorChange = () => {
    setPendingColorChange(null);
    editingColorIndexRef.current = null;
  };

  return (
    <aside className="w-64 bg-gray-800 text-white p-4 flex flex-col h-full shadow-2xl overflow-y-auto">
      <h1 className="text-2xl font-bold text-center mb-4 text-blue-400">Battle Planner</h1>
      <input
        type="color"
        ref={colorInputRef}
        className="hidden"
        onChange={handleColorInputChange}
        onBlur={handleCancelColorChange}
      />
      
      <div className="border-t border-gray-700 pt-4">
        <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">TOOLS</h2>
        <ToolButton label="Select (S)" isActive={activeTool === 'select'} onClick={() => onToolSelect('select')}><SelectIcon /></ToolButton>
        <ToolButton label="Move (M)" isActive={activeTool === 'move'} onClick={() => onToolSelect('move')}><MoveIcon /></ToolButton>
        <ToolButton label="Clone (C)" isActive={activeTool === 'clone'} onClick={() => onToolSelect('clone')}><CloneIcon /></ToolButton>
        <ToolButton label="Zoom (Z)" isActive={activeTool === 'zoom'} onClick={() => onToolSelect('zoom')}><ZoomIcon /></ToolButton>
        <ToolButton label="Enlarge (E)" isActive={activeTool === 'enlarge'} onClick={() => onToolSelect('enlarge')}><EnlargeIcon /></ToolButton>
        <ToolButton label="Text (T)" isActive={activeTool === 'text'} onClick={() => onToolSelect('text')}><TextIcon /></ToolButton>
        <ToolButton label="Eraser (B)" isActive={activeTool === 'eraser'} onClick={() => onToolSelect('eraser')}><EraserIcon /></ToolButton>
        <ToolButton label="Draw Path" isActive={activeTool === 'path'} onClick={() => onToolSelect('path')}><PathIcon /></ToolButton>
        <ToolButton label="Draw Arrow" isActive={activeTool === 'arrow'} onClick={() => onToolSelect('arrow')}><ArrowIcon /></ToolButton>
        <ToolButton label="Draw Circle" isActive={activeTool === 'circle'} onClick={() => onToolSelect('circle')}><CircleIcon /></ToolButton>
      </div>

      {activeTool === 'text' && (
        <div className="mt-4 p-3 bg-gray-700 rounded-lg space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-sm font-semibold text-gray-300">Text Creator</h3>
            <div className="flex items-center space-x-1">
              <button onClick={onAddTextPreset} title="Save Preset" className="p-1 text-gray-400 hover:text-white transition-colors"><BookmarkSquareIcon /></button>
              <div className="relative">
                <button onClick={() => setIsPresetListOpen(p => !p)} title="Load Preset" className="p-1 text-gray-400 hover:text-white transition-colors"><ChevronDownIcon /></button>
                {isPresetListOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-20 max-h-60 overflow-y-auto">
                        {textPresets.length > 0 ? textPresets.map(preset => (
                            <div key={preset.id} className="flex items-center justify-between text-sm text-white hover:bg-gray-600">
                                <span onClick={() => { onLoadTextPreset(preset); setIsPresetListOpen(false); }} className="flex-1 py-1.5 pl-3 pr-2 cursor-pointer truncate">{preset.content}</span>
                                <button 
                                  onClick={(e) => { e.stopPropagation(); onDeleteTextPreset(preset.id); }} 
                                  title={`Delete "${preset.content}"`}
                                  className="p-1.5 text-gray-400 hover:text-red-500 flex-shrink-0 rounded-md hover:bg-gray-700"
                                >
                                  <div className="w-3.5 h-3.5">
                                    <TrashIcon />
                                  </div>
                                </button>
                            </div>
                        )) : <div className="p-2 text-sm text-gray-400">No saved presets.</div>}
                    </div>
                )}
              </div>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Text</label>
            <input
              type="text"
              value={newText.content}
              onChange={e => onNewTextChange({ ...newText, content: e.target.value })}
              className="w-full bg-gray-800 text-white p-2 rounded-md border border-gray-600 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Font</label>
            <select
              value={newText.fontFamily}
              onChange={e => onNewTextChange({ ...newText, fontFamily: e.target.value })}
              className="w-full bg-gray-800 text-white p-2 rounded-md border border-gray-600 focus:ring-blue-500 focus:border-blue-500"
            >
              {FONT_FACES.map(font => <option key={font} value={font}>{font}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">Preview</label>
            <div
              draggable
              onDragStart={handleTextDragStart}
              className="p-4 bg-gray-800 rounded-md cursor-grab active:cursor-grabbing border-2 border-dashed border-gray-500 flex items-center justify-center text-center"
              style={{ minHeight: '80px' }}
            >
              <span style={{ fontSize: `${newText.size}rem`, fontFamily: newText.fontFamily, color: selectedColor }}>
                {newText.content}
              </span>
            </div>
            <div className="flex items-center justify-center mt-2 space-x-2">
                <button onClick={() => handleTextSizeChange(-0.1)} className="p-1.5 bg-gray-600 rounded-full hover:bg-gray-500 transition-colors"><MinusIcon /></button>
                <span className="text-sm font-mono text-gray-300 w-12 text-center">{newText.size.toFixed(1)}rem</span>
                <button onClick={() => handleTextSizeChange(0.1)} className="p-1.5 bg-gray-600 rounded-full hover:bg-gray-500 transition-colors"><PlusIcon /></button>
            </div>
          </div>
        </div>
      )}

      {selectedToken && (
        <div className="border-t border-gray-700 pt-4 mt-4">
          <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">SELECTED TOKEN</h2>
          <div className="p-2 space-y-4 bg-gray-700 rounded-lg">
            
            {/* --- ROTATION --- */}
            <div>
              <div className="flex justify-between items-center mb-1 px-1">
                <label className="text-sm font-medium text-gray-300">Angle</label>
                <span className="font-mono text-sm text-gray-300">{Math.round(selectedToken.rotation ?? 0)}°</span>
              </div>
              <input
                type="range"
                min="-180"
                max="180"
                step="1"
                value={selectedToken.rotation ?? 0}
                onChange={(e) => onTokenUpdate(selectedToken.id, { rotation: parseFloat(e.target.value) })}
                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
              />
            </div>
            
            {/* --- ANIMATION --- */}
            <div>
              <div className="flex justify-between items-center mb-1 px-1">
                <label className="text-sm font-medium text-gray-300">Speed</label>
                <span className="font-mono text-sm text-gray-300">{(selectedToken.animationSpeed ?? 1.0).toFixed(1)}x</span>
              </div>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={selectedToken.animationSpeed ?? 1.0}
                onChange={(e) => onTokenUpdate(selectedToken.id, { animationSpeed: parseFloat(e.target.value)})}
                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
              />
            </div>
            <div>
              <label className="flex items-center space-x-3 cursor-pointer px-1">
                <input
                  type="checkbox"
                  checked={selectedToken.isPatrol ?? false}
                  onChange={(e) => onTokenUpdate(selectedToken.id, { isPatrol: e.target.checked })}
                  className="w-4 h-4 text-blue-500 bg-gray-600 border-gray-500 rounded focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                />
                <span className="text-sm font-medium text-gray-300">Patrol Mode</span>
              </label>
            </div>
            
            {/* --- PATH ACTIONS --- */}
            <div className="pt-2">
                <button
                    onClick={() => onClearPath(selectedToken.id)}
                    className="w-full bg-yellow-600 hover:bg-yellow-500 text-white font-bold py-2 px-4 rounded transition-colors"
                >
                    Clear Selected Path
                </button>
            </div>

          </div>
        </div>
      )}

      <div className="border-t border-gray-700 pt-4 mt-4">
        <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">GLOBAL SETTINGS</h2>
        <div className="p-2 space-y-2 bg-gray-700 rounded-lg">
            <div>
              <div className="flex justify-between items-center mb-1 px-1">
                <label className="text-sm font-medium text-gray-300">All Tokens Size</label>
                <span className="font-mono text-sm text-gray-300">{globalTokenSize}%</span>
              </div>
              <input
                type="range"
                min="-100"
                max="100"
                step="1"
                value={globalTokenSize}
                onChange={(e) => onGlobalTokenSizeChange(parseInt(e.target.value, 10))}
                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
              />
            </div>
        </div>
      </div>

      <div className="border-t border-gray-700 pt-4 mt-4">
        <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">COLOR</h2>
        <div className="grid grid-cols-7 gap-1.5 p-1">
            {paletteColors.map((color, index) => (
                <button
                    key={`${color}-${index}`}
                    title={`${color} (double-click to change)`}
                    onClick={() => onColorChange(color)}
                    onDoubleClick={() => handleColorDoubleClick(index)}
                    className={`w-7 h-7 rounded-full transition-transform duration-150 ${selectedColor === color ? 'ring-2 ring-offset-2 ring-offset-gray-800 ring-white' : 'ring-1 ring-transparent hover:ring-white'}`}
                    style={{ backgroundColor: color }}
                />
            ))}
        </div>
        {pendingColorChange && (
            <div className="mt-2 p-2 bg-gray-700 rounded-lg flex items-center justify-between">
                <div className="flex items-center gap-2">
                    <span className="text-sm">New:</span>
                    <div className="w-5 h-5 rounded-full ring-1 ring-white" style={{ backgroundColor: pendingColorChange.color }}></div>
                </div>
                <div className="flex gap-2">
                    <button onClick={handleConfirmColorChange} className="bg-blue-600 hover:bg-blue-500 text-white px-3 py-1 text-sm rounded">OK</button>
                    <button onClick={handleCancelColorChange} className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 text-sm rounded">Cancel</button>
                </div>
            </div>
        )}
      </div>

      <div className="border-t border-gray-700 pt-4 mt-4">
        <div className="flex items-center justify-between mb-2 px-2">
          <h2 className="text-sm font-semibold text-gray-400">UNITS</h2>
          <label title="Upload Custom Unit (PNG)" className="p-1.5 text-gray-400 hover:text-white rounded-md hover:bg-gray-700 cursor-pointer transition-colors">
            <div className="w-5 h-5"><UploadIcon /></div>
            <input type="file" className="hidden" accept="image/png" multiple onChange={onCustomUnitUpload} />
          </label>
        </div>
        <div className="grid grid-cols-3 gap-2">
            {UNIT_TYPES.map(type => (
              <div
                key={type}
                title={type}
                draggable
                onDragStart={(e) => handleUnitDragStart(e, type)}
                className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center"
                style={{ backgroundColor: selectedColor }}
              >
                <div className="w-8 h-8 text-white">{unitIcons[type]}</div>
              </div>
            ))}
            {customUnits.map(unit => (
              <div
                key={unit.id}
                title={unit.name}
                draggable
                onDragStart={(e) => handleCustomUnitDragStart(e, unit)}
                className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center"
                style={{ backgroundColor: selectedColor }}
              >
                <img src={unit.imageData} alt={unit.name} className="w-8 h-8 object-contain" />
              </div>
            ))}
        </div>
      </div>

      <div className="border-t border-gray-700 pt-4 mt-4">
        <div className="flex items-center justify-between mb-2 px-2">
          <h2 className="text-sm font-semibold text-gray-400">ARTILLERY</h2>
          <label title="Upload Custom Artillery (PNG)" className="p-1.5 text-gray-400 hover:text-white rounded-md hover:bg-gray-700 cursor-pointer transition-colors">
            <div className="w-5 h-5"><UploadIcon /></div>
            <input type="file" className="hidden" accept="image/png" multiple onChange={onCustomArtilleryUpload} />
          </label>
        </div>
          <div className="grid grid-cols-3 gap-2">
            {ARTILLERY_TYPES.map(unit => (
              <div
                key={unit.name}
                title={unit.name}
                draggable
                onDragStart={(e) => handleArtilleryDragStart(e, unit.type)}
                className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center"
                style={{ backgroundColor: selectedColor }}
              >
                <div className="w-8 h-8 text-white">{artilleryIcons[unit.type]}</div>
              </div>
            ))}
            {customArtillery.map(unit => (
              <div
                key={unit.id}
                title={unit.name}
                draggable
                onDragStart={(e) => handleCustomArtilleryDragStart(e, unit)}
                className="p-2 rounded-lg cursor-grab active:cursor-grabbing flex justify-center items-center"
                style={{ backgroundColor: selectedColor }}
              >
                <img src={unit.imageData} alt={unit.name} className="w-8 h-8 object-contain" />
              </div>
            ))}
          </div>
      </div>
      
      <div className="border-t border-gray-700 pt-4 mt-4">
        <h2 className="text-sm font-semibold text-gray-400 mb-2 px-2">HISTORY</h2>
        <div className="flex items-center space-x-2 px-1">
            <button
                onClick={onUndo}
                title="Undo (Ctrl+Z)"
                className="w-1/2 flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200"
            >
                <div className="w-5 h-5"><UndoIcon /></div>
                <span className="ml-2 font-medium text-sm">Undo</span>
            </button>
            <button
                onClick={onRedo}
                title="Redo (Ctrl+Y)"
                className="w-1/2 flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-gray-300 p-2 rounded-lg transition-colors duration-200"
            >
                <div className="w-5 h-5"><RedoIcon /></div>
                <span className="ml-2 font-medium text-sm">Redo</span>
            </button>
        </div>
      </div>

      <div className="mt-auto pt-4 border-t border-gray-700">
        <div className="grid grid-cols-2 gap-2">
            <label title="Import Project" className="flex items-center justify-center bg-blue-600 hover:bg-blue-500 text-white font-bold py-2 px-2 rounded transition-colors cursor-pointer text-center">
                <div className="w-5 h-5"><ImportIcon /></div>
                <span className="ml-2 text-sm">Import</span>
                <input type="file" className="hidden" accept=".json" onChange={onImportProject} />
            </label>
            <button onClick={onExportProject} title="Export Project" className="flex items-center justify-center bg-blue-600 hover:bg-blue-500 text-white font-bold py-2 px-2 rounded transition-colors text-center">
                <div className="w-5 h-5"><ExportIcon /></div>
                <span className="ml-2 text-sm">Export</span>
            </button>
            <label title="Upload Map" className="flex items-center justify-center bg-gray-600 hover:bg-gray-500 text-white font-bold py-2 px-2 rounded transition-colors cursor-pointer text-center">
                <div className="w-5 h-5"><UploadIcon /></div>
                <span className="ml-2 text-sm">Map</span>
                <input type="file" className="hidden" accept="image/*" onChange={onMapUpload} />
            </label>
            <button onClick={onClearAll} title="Clear All" className="flex items-center justify-center bg-red-600 hover:bg-red-500 text-white font-bold py-2 px-2 rounded transition-colors">
                <div className="w-5 h-5"><TrashIcon /></div>
                <span className="ml-2 text-sm">Clear</span>
            </button>
        </div>
      </div>
    </aside>
  );
};
