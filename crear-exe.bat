@echo off
echo ========================================
echo  CREAR EJECUTABLE .EXE
echo  Battle Strategy Creator
echo ========================================
echo.

REM Verificar si Node.js está instalado
echo [1/5] Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js no está instalado.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js está disponible
)

REM Verificar si las dependencias están instaladas
echo.
echo [2/5] Verificando dependencias...
if not exist "node_modules" (
    echo ❌ Dependencias no instaladas.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Dependencias instaladas
)

REM Verificar si Electron está instalado
echo.
echo [3/5] Verificando Electron...
if not exist "node_modules\electron" (
    echo ❌ Electron no está instalado.
    echo Instalando Electron...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Error al instalar Electron.
        pause
        exit /b 1
    )
) else (
    echo ✅ Electron está instalado
)

REM Compilar la aplicación React
echo.
echo [4/5] Compilando aplicación React...
echo Esto puede tomar unos minutos...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Error al compilar la aplicación.
    echo Verifica que no haya errores en el código.
    pause
    exit /b 1
) else (
    echo ✅ Aplicación compilada correctamente
)

REM Crear el ejecutable
echo.
echo [5/5] Creando ejecutable .exe...
echo Esto puede tomar varios minutos...
echo ⏳ Generando instalador para Windows...
npm run dist
if %errorlevel% neq 0 (
    echo ❌ Error al crear el ejecutable.
    echo Verifica los logs anteriores para más detalles.
    pause
    exit /b 1
) else (
    echo ✅ Ejecutable creado correctamente
)

echo.
echo ========================================
echo  ✅ EJECUTABLE CREADO EXITOSAMENTE
echo ========================================
echo.
echo 📁 El ejecutable se encuentra en: dist-electron\
echo.
echo Archivos generados:
echo - Instalador: Battle Strategy Creator Setup.exe
echo - Aplicación portable: win-unpacked\Battle Strategy Creator.exe
echo.
echo 🚀 Para distribuir tu aplicación:
echo    - Usa el instalador para instalación completa
echo    - Usa la carpeta win-unpacked para versión portable
echo.
pause

REM Abrir la carpeta con los archivos generados
echo ¿Quieres abrir la carpeta con los archivos generados? (S/N)
set /p choice=
if /i "%choice%"=="S" (
    start explorer dist-electron
)
