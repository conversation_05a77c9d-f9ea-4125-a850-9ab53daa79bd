import React, { useState, useMemo, useRef, useEffect } from 'react';
import { type TokenState, type Annotation, UnitType, AnnotationType, ArtilleryType, type LayerEntry, type LayerItemIdentifier, type LayerGroup } from '../types';
import { 
    SwordIcon, ShieldIcon, PikeIcon, ArcherIcon, CavalryIcon, TextIcon as UnitTextIcon, 
    CannonIcon, MetrallaIcon, MortarIcon, BallistaIcon, RocketLauncherIcon, SiegeTowerIcon, TrebuchetIcon, CulverinIcon
} from './icons/UnitIcons';
import { ArrowIcon, CircleIcon, EyeIcon, EyeSlashIcon, TrashIcon, ChevronRightIcon, LayersIcon, ChevronDownIcon, PlusIcon } from './icons/ToolIcons';

interface LayersPanelProps {
  layers: LayerEntry[];
  tokens: TokenState[];
  annotations: Annotation[];
  selectedTokenId: number | null;
  onSelectToken: (id: number) => void;
  onToggleVisibility: (id: number, type: 'token' | 'annotation') => void;
  onToggleGroupVisibility: (groupId: number) => void;
  onDeleteItem: (id: number, type: 'token' | 'annotation') => void;
  onLayersUpdate: (layers: LayerEntry[]) => void;
  getNextId: () => number;
}

const itemIcons: Record<string, React.ReactNode> = {
  [UnitType.Sword]: <SwordIcon />, [UnitType.Shield]: <ShieldIcon />, [UnitType.Pike]: <PikeIcon />,
  [UnitType.Archer]: <ArcherIcon />, [UnitType.Cavalry]: <CavalryIcon />, [UnitType.Text]: <UnitTextIcon />,
  [UnitType.Artillery]: <CannonIcon />, [AnnotationType.Arrow]: <ArrowIcon />, [AnnotationType.Circle]: <CircleIcon />,
  'group': <LayersIcon />, [UnitType.Custom]: <ShieldIcon/>, [UnitType.CustomArtillery]: <CannonIcon/>
};

const artilleryIcons: Record<ArtilleryType, React.ReactNode> = {
    [ArtilleryType.Cannon]: <CannonIcon />, [ArtilleryType.Metralla]: <MetrallaIcon />, [ArtilleryType.Mortar]: <MortarIcon />,
    [ArtilleryType.Ballista]: <BallistaIcon />, [ArtilleryType.RocketLauncher]: <RocketLauncherIcon />,
    [ArtilleryType.SiegeTower]: <SiegeTowerIcon />, [ArtilleryType.Trebuchet]: <TrebuchetIcon />, [ArtilleryType.Culverin]: <CulverinIcon />,
};

const hexToRgba = (hex: string, alpha: number): string => {
    if(!hex) return `rgba(100, 116, 139, ${alpha})`;
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const getItemData = (
    itemIdentifier: LayerEntry,
    itemMap: Map<string, TokenState | Annotation>
): { name: string; icon: React.ReactNode; color: string, isVisible: boolean, id: number } => {
    if (itemIdentifier.type === 'group') {
        return { name: itemIdentifier.name, icon: itemIcons.group, color: '#94a3b8', isVisible: true, id: itemIdentifier.id };
    }

    const item = itemMap.get(`${itemIdentifier.type}-${itemIdentifier.id}`);
    if (!item) {
        return { name: 'Unknown', icon: <></>, color: '#94a3b8', isVisible: false, id: -1 };
    }

    let icon = itemIcons[item.type];
    if (item.type === UnitType.Artillery && 'artilleryType' in item && item.artilleryType) {
        icon = artilleryIcons[item.artilleryType] ?? itemIcons[UnitType.Artillery];
    } else if (item.type === UnitType.Custom || item.type === UnitType.CustomArtillery){
        icon = item.type === UnitType.Custom ? itemIcons[UnitType.Custom] : itemIcons[UnitType.CustomArtillery]
    }

    let name: string;
    if ('name' in item && item.name) {
        name = item.name;
    } else if (item.type === UnitType.Text && 'text' in item) {
        name = item.text || 'Text';
    } else if (item.type === UnitType.Artillery && 'artilleryType' in item && item.artilleryType) {
        name = `${item.artilleryType}`;
    } else {
        name = `${item.type.charAt(0).toUpperCase() + item.type.slice(1)} ${item.id}`;
    }
    
    return { name, icon, color: item.color, isVisible: item.isVisible ?? true, id: item.id };
};


export const LayersPanel: React.FC<LayersPanelProps> = ({
  layers, tokens, annotations, selectedTokenId, onSelectToken,
  onToggleVisibility, onToggleGroupVisibility, onDeleteItem, onLayersUpdate, getNextId
}) => {
    const [isCollapsed, setIsCollapsed] = useState(true);
    const [isCreatingGroup, setIsCreatingGroup] = useState(false);
    const [newGroupName, setNewGroupName] = useState('New Group');
    const [activeDropdownPath, setActiveDropdownPath] = useState<string | null>(null);
    const [dropdownPositionClass, setDropdownPositionClass] = useState('bottom-full mb-1');
    const dropdownRef = useRef<HTMLDivElement>(null);

    const itemMap = useMemo(() => {
        const map = new Map<string, TokenState | Annotation>();
        tokens.forEach(t => map.set(`token-${t.id}`, t));
        annotations.forEach(a => map.set(`annotation-${a.id}`, a));
        return map;
    }, [tokens, annotations]);

    const groups = useMemo(() => layers.filter(l => l.type === 'group') as LayerGroup[], [layers]);
    
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (activeDropdownPath && dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setActiveDropdownPath(null);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [activeDropdownPath]);

    const handleCreateGroup = () => {
        if (!newGroupName.trim()) return;
    
        const existingGroupNames = new Set<string>();
        const collectGroupNames = (entries: LayerEntry[]) => {
            entries.forEach(entry => {
                if (entry.type === 'group') {
                    existingGroupNames.add(entry.name);
                }
            });
        };
        collectGroupNames(layers);
    
        let finalName = newGroupName.trim();
        if (existingGroupNames.has(finalName)) {
            let counter = 2;
            while (existingGroupNames.has(`${newGroupName.trim()} (${counter})`)) {
                counter++;
            }
            finalName = `${newGroupName.trim()} (${counter})`;
        }
    
        const newGroup: LayerGroup = {
            id: getNextId(),
            type: 'group',
            name: finalName,
            items: [],
            isCollapsed: false,
        };
    
        onLayersUpdate([newGroup, ...layers]);
        setIsCreatingGroup(false);
        setNewGroupName('New Group');
    };

    const handleAssignToGroup = (itemIdent: LayerItemIdentifier, targetGroupId: number | null) => {
        let newLayers = JSON.parse(JSON.stringify(layers));
        let itemToMove: LayerItemIdentifier | null = null;
    
        const findAndRemove = (entries: LayerEntry[]): LayerEntry[] => {
            const remaining: LayerEntry[] = [];
            for (const entry of entries) {
                if (entry.type !== 'group' && entry.id === itemIdent.id && entry.type === itemIdent.type) {
                    itemToMove = entry;
                } else {
                    if (entry.type === 'group') {
                        entry.items = findAndRemove(entry.items);
                    }
                    remaining.push(entry);
                }
            }
            return remaining;
        };
        newLayers = findAndRemove(newLayers);
        
        if (!itemToMove) return;
    
        if (targetGroupId === null) {
            newLayers.push(itemToMove);
        } else {
            let targetGroupFound = false;
            const addToGroup = (entries: LayerEntry[]) => {
                for (const entry of entries) {
                    if (entry.type === 'group' && entry.id === targetGroupId) {
                        entry.items.push(itemToMove!);
                        targetGroupFound = true;
                        return;
                    }
                }
            };
            addToGroup(newLayers);
             if (!targetGroupFound) {
                newLayers.push(itemToMove);
            }
        }
        
        const cleanupEmpty = (entries: LayerEntry[]): LayerEntry[] => {
            return entries.filter(e => e.type !== 'group' || (e.items && e.items.length > 0));
        };
    
        onLayersUpdate(cleanupEmpty(newLayers));
        setActiveDropdownPath(null);
    };

    const handleToggleDropdown = (e: React.MouseEvent, path: string) => {
        e.stopPropagation();
        if (activeDropdownPath === path) {
            setActiveDropdownPath(null);
            return;
        }
    
        const buttonElement = e.currentTarget;
        const rect = buttonElement.getBoundingClientRect();
        
        const dropdownHeight = Math.min(192, (groups.length + 1) * 32 + 16); // 192px is max-h-48, 32px per item
    
        if (rect.top < dropdownHeight) {
            // Not enough space above, open below
            setDropdownPositionClass('top-full mt-1');
        } else {
            // Default, open above
            setDropdownPositionClass('bottom-full mb-1');
        }
        
        setActiveDropdownPath(path);
    };
    
    const toggleCollapse = (path: string) => {
        const newLayers = JSON.parse(JSON.stringify(layers));
        const indices = path.split('-').map(Number);
        let current: any = { items: newLayers };
        indices.forEach(index => {
            current = current.items[index];
        });
        if (current && current.type === 'group') {
            current.isCollapsed = !current.isCollapsed;
        }
        onLayersUpdate(newLayers);
    };

    const flatItems: { item: LayerEntry; path: string; depth: number }[] = [];
    const buildFlatList = (entries: LayerEntry[], pathPrefix: string, depth: number) => {
        const reversedEntries = [...entries].reverse();
        reversedEntries.forEach((entry, reversedIndex) => {
            const originalIndex = entries.length - 1 - reversedIndex;
            const currentPath = pathPrefix ? `${pathPrefix}-${originalIndex}` : `${originalIndex}`;
            
            flatItems.push({ item: entry, path: currentPath, depth });
            
            if (entry.type === 'group' && !entry.isCollapsed) {
                buildFlatList(entry.items, currentPath, depth + 1);
            }
        });
    };
    buildFlatList(layers, '', 0);

    if (isCollapsed) {
        return (
            <div className="absolute top-4 right-4 z-10">
                <button
                    onClick={() => setIsCollapsed(false)}
                    title="Open Layers"
                    className="w-14 h-14 bg-blue-600 rounded-xl shadow-lg flex items-center justify-center transform rotate-45 hover:scale-110 transition-all duration-200 ease-in-out"
                >
                    <div className="transform -rotate-45 text-white w-8 h-8"><LayersIcon /></div>
                </button>
            </div>
        );
    }
    
  return (
    <aside className="absolute top-4 right-4 z-10 w-72 bg-gray-900/70 backdrop-blur-md rounded-lg shadow-2xl flex flex-col max-h-[calc(100vh-32px)]">
        <div className="flex items-center justify-between p-3 border-b border-white/10 flex-shrink-0">
            <h1 className="text-lg font-bold text-blue-400">Layers</h1>
            <div className="flex items-center space-x-2">
                {isCreatingGroup ? (
                    <div className="flex items-center">
                        <input
                            type="text"
                            value={newGroupName}
                            onChange={(e) => setNewGroupName(e.target.value)}
                            onKeyDown={(e) => { if (e.key === 'Enter') handleCreateGroup(); if (e.key === 'Escape') setIsCreatingGroup(false); }}
                            className="bg-gray-700 text-white text-sm p-1 rounded-l-md outline-none w-28"
                            autoFocus
                        />
                        <button onClick={handleCreateGroup} className="bg-blue-600 hover:bg-blue-500 p-1.5 rounded-r-md text-white">✓</button>
                        <button onClick={() => setIsCreatingGroup(false)} className="bg-red-600 hover:bg-red-500 p-1.5 rounded-md ml-1 text-white">X</button>
                    </div>
                ) : (
                    <button onClick={() => setIsCreatingGroup(true)} title="Create New Group" className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"><PlusIcon /></button>
                )}
                 <button onClick={() => setIsCollapsed(true)} title="Collapse Layers" className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-white/10 transition-colors"><ChevronRightIcon /></button>
            </div>
        </div>
      
        <div className="overflow-y-auto p-2">
            {flatItems.length === 0 && <p className="text-xs text-gray-500 px-2 py-1">No items on map.</p>}
            {flatItems.map(({ item, path, depth }) => {
                const data = getItemData(item, itemMap);
                const isGroup = item.type === 'group';
                const isSelected = !isGroup && selectedTokenId === (item as LayerItemIdentifier).id;

                const isGroupVisible = isGroup ? (item as LayerGroup).items.some(child => {
                    const childData = itemMap.get(`${(child as LayerItemIdentifier).type}-${(child as LayerItemIdentifier).id}`);
                    return childData?.isVisible ?? true;
                }) : false;

                return (
                    <div
                        key={path}
                        onClick={() => {
                          if (isGroup) toggleCollapse(path);
                          else onSelectToken(data.id);
                        }}
                        className={`flex items-center p-1.5 my-0.5 rounded-md cursor-pointer transition-all duration-150 ${isSelected ? 'ring-2 ring-blue-400' : ''}`}
                        style={{ marginLeft: `${depth * 16}px`, backgroundColor: hexToRgba(data.color, 0.2) }}
                    >
                        {isGroup && (
                          <button onClick={(e) => { e.stopPropagation(); toggleCollapse(path); }} className={`w-4 h-4 mr-1 text-gray-300 transition-transform ${(item as LayerGroup).isCollapsed ? '' : 'rotate-90'}`}>
                            <ChevronRightIcon />
                          </button>
                        )}
                        <div className="w-4 h-4 mr-2 flex-shrink-0" style={{ color: isGroup ? 'white' : data.color }}>{data.icon}</div>
                        <span className="flex-grow truncate text-xs font-medium text-gray-200">{data.name}</span>
                        
                        <div className="flex items-center flex-shrink-0 ml-2">
                             {isGroup && (
                                <button onClick={(e) => { e.stopPropagation(); onToggleGroupVisibility(item.id); }} title="Toggle Group Visibility" className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4">{isGroupVisible ? <EyeIcon /> : <EyeSlashIcon />}</div></button>
                            )}
                            {!isGroup && (
                                <div className="flex items-center">
                                    <div className="relative">
                                        <button onClick={(e) => handleToggleDropdown(e, path)} title="Assign to group" className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4"><LayersIcon /></div></button>
                                        {activeDropdownPath === path && (
                                            <div ref={dropdownRef} className={`absolute right-0 w-40 bg-gray-800 border border-gray-600 rounded-md shadow-lg z-20 max-h-48 overflow-y-auto ${dropdownPositionClass}`}>
                                                <div onClick={(e) => { e.stopPropagation(); handleAssignToGroup(item as LayerItemIdentifier, null); }} className="px-3 py-1.5 text-sm text-white hover:bg-gray-700 cursor-pointer">Ninguno</div>
                                                {groups.map(g => (
                                                    <div key={g.id} onClick={(e) => { e.stopPropagation(); handleAssignToGroup(item as LayerItemIdentifier, g.id); }} className="px-3 py-1.5 text-sm text-white hover:bg-gray-700 cursor-pointer">{g.name}</div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <button onClick={(e) => { e.stopPropagation(); onToggleVisibility(data.id, (item as LayerItemIdentifier).type); }} title="Toggle Visibility" className="p-1 text-gray-400 hover:text-white"><div className="w-4 h-4">{data.isVisible ? <EyeIcon /> : <EyeSlashIcon />}</div></button>
                                    <button onClick={(e) => { e.stopPropagation(); onDeleteItem(data.id, (item as LayerItemIdentifier).type); }} title="Delete Item" className="p-1 text-gray-400 hover:text-red-500"><div className="w-4 h-4"><TrashIcon /></div></button>
                                </div>
                            )}
                        </div>
                    </div>
                );
            })}
      </div>
    </aside>
  );
};