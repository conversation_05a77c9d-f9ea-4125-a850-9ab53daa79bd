export enum UnitType {
  Shield = 'Shield',
  Pike = 'Pike',
  <PERSON> = 'Archer',
  Cavalry = 'Cavalry',
  Sword = 'Sword',
  Text = 'Text',
  Artillery = 'Artillery',
  Custom = 'Custom',
  CustomArtillery = 'CustomArtillery',
}

export enum ArtilleryType {
  Cannon = 'Cannon',
  Metralla = 'Metralla',
  Mortar = 'Mortar',
  Ballista = 'Ballista',
  RocketLauncher = 'Rocket Launcher',
  SiegeTower = 'Siege Tower',
  Trebuchet = 'Trebuchet',
  Culverin = 'Culverin',
}

export interface Point {
  x: number;
  y: number;
}

export interface TokenState {
  id: number;
  type: UnitType;
  color: string;
  number: number | null;
  position: Point;
  path: Point[];
  animationProgress: number; // 0 to 1
  patrolForward?: boolean;
  size?: number; // scale factor
  text?: string;
  fontFamily?: string;
  artilleryType?: ArtilleryType;
  rotation?: number; // in degrees
  isVisible?: boolean;
  animationSpeed?: number;
  isPatrol?: boolean;
  customImage?: string;
  name?: string;
}

export enum AnnotationType {
  Arrow = 'Arrow',
  Circle = 'Circle',
}

export interface ArrowAnnotation {
  type: AnnotationType.Arrow;
  id: number;
  start: Point;
  end: Point;
  control: Point;
  color: string;
  isVisible?: boolean;
}

export interface CircleAnnotation {
  type: AnnotationType.Circle;
  id: number;
  center: Point;
  radius: number;
  color: string;
  isVisible?: boolean;
}

export type Annotation = ArrowAnnotation | CircleAnnotation;

export interface CustomUnit {
  id: number;
  name: string;
  imageData: string;
}

export interface TextPreset {
  id: number;
  content: string;
  size: number;
  fontFamily: string;
}

export type Tool = 'select' | 'move' | 'clone' | 'path' | 'arrow' | 'circle' | 'eraser' | 'text' | 'zoom' | 'enlarge';

export type DrawingState =
  | { type: 'path'; start: Point; current: Point; pathForTokenId: number }
  | { type: 'circle'; start: Point; current: Point }
  | { type: 'arrow'; stage: 'defining-end'; start: Point; current: Point }
  | { type: 'arrow'; stage: 'defining-control'; start: Point; end: Point; current: Point }
  | { type: 'moving'; tokenId: number; offset: Point; current: Point }
  | { type: 'cloning'; tokenToClone: TokenState; current: Point }
  | { type: 'zoom'; start: Point; current: Point }
  | { type: 'enlarging'; tokenId: number; startY: number; startSize: number; current: Point };

export interface ViewTransform {
  scale: number;
  translateX: number;
  translateY: number;
}

export type LayerItemIdentifier = {
  id: number;
  type: 'token' | 'annotation';
};

export interface LayerGroup {
  id: number;
  type: 'group';
  name: string;
  items: LayerEntry[];
  isCollapsed: boolean;
}

export type LayerEntry = LayerItemIdentifier | LayerGroup;
  
export interface ProjectData {
  mapImage: string | null;
  mapDimensions: { width: number; height: number; } | null;
  tokens: TokenState[];
  annotations: Annotation[];
  paletteColors: string[];
  nextId: number;
  customUnits?: CustomUnit[];
  customArtillery?: CustomUnit[];
  layers?: LayerEntry[];
}