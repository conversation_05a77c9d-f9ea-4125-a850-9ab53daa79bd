@echo off
echo ========================================
echo  Battle Strategy Creator - Electron
echo ========================================
echo.

REM Verificar si Node.js está instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js no está instalado.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias están instaladas
if not exist "node_modules" (
    echo ❌ Dependencias no instaladas.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
)

REM Verificar si Electron está instalado
if not exist "node_modules\electron" (
    echo ❌ Electron no está instalado.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
)

echo ✅ Iniciando Battle Strategy Creator en Electron...
echo.
echo 🖥️  La aplicación se abrirá como aplicación de escritorio
echo.
echo Para detener la aplicación, cierra la ventana o presiona Ctrl+C aquí
echo.
echo ========================================
echo.

REM Ejecutar la aplicación Electron con el servidor de desarrollo
npm run electron-dev

echo.
echo ========================================
echo  Aplicación cerrada
echo ========================================
pause
