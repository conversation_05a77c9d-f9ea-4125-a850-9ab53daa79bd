# 🚀 Battle Strategy Creator - Crear Ejecutable .EXE

## 📋 Archivos .bat disponibles:

### 1. `instalar.bat` - Instalación inicial
- Instala Node.js (si no está instalado)
- Instala todas las dependencias incluyendo Electron
- Configura el proyecto

### 2. `ejecutar.bat` - Ejecutar en navegador
- Ejecuta la aplicación en el navegador web
- Abre automáticamente http://localhost:5173

### 3. `ejecutar-electron.bat` - Ejecutar como aplicación de escritorio
- Ejecuta la aplicación como una aplicación de escritorio nativa
- Usa Electron para crear una ventana independiente

### 4. `crear-exe.bat` - Crear ejecutable .EXE
- Compila la aplicación React
- Genera un ejecutable .exe para Windows
- Crea un instalador completo

## 🔧 Pasos para crear el ejecutable:

1. **Ejecuta `instalar.bat`** (solo la primera vez)
   - Instala todas las dependencias necesarias

2. **Prueba con `ejecutar-electron.bat`**
   - Verifica que la aplicación funciona como aplicación de escritorio

3. **Ejecuta `crear-exe.bat`**
   - Genera el ejecutable final

## 📁 Archivos generados:

Después de ejecutar `crear-exe.bat`, encontrarás en la carpeta `dist-electron/`:

- **`Battle Strategy Creator Setup.exe`** - Instalador completo
- **`win-unpacked/`** - Carpeta con la aplicación portable
  - `Battle Strategy Creator.exe` - Ejecutable principal

## 🎯 Opciones de distribución:

### Instalador completo:
- Usa `Battle Strategy Creator Setup.exe`
- Se instala en el sistema como cualquier programa
- Crea accesos directos en el escritorio y menú inicio

### Versión portable:
- Usa la carpeta `win-unpacked/`
- No requiere instalación
- Puedes copiar la carpeta completa a cualquier PC

## 🔧 Personalización:

### Cambiar icono:
1. Reemplaza `electron/icon.png` con tu icono (256x256 px)
2. Crea `electron/icon.ico` para Windows
3. Ejecuta `crear-exe.bat` nuevamente

### Cambiar nombre de la aplicación:
1. Edita `package.json` en la sección `"build"`
2. Cambia `"productName"` por el nombre deseado
3. Ejecuta `crear-exe.bat` nuevamente

## ⚠️ Requisitos:

- Windows 10 o superior
- Node.js 18 o superior
- Al menos 2GB de espacio libre (para dependencias y compilación)
- Conexión a internet (para descargar dependencias)

## 🐛 Solución de problemas:

### Error "npm no se reconoce":
- Instala Node.js desde https://nodejs.org/
- Reinicia la terminal después de la instalación

### Error al crear el .exe:
- Verifica que no haya errores en el código
- Ejecuta `npm run build` manualmente para ver errores
- Asegúrate de tener suficiente espacio en disco

### El .exe no funciona en otros PCs:
- Usa el instalador `Setup.exe` en lugar del ejecutable directo
- Verifica que el PC de destino tenga Windows 10 o superior

## 📞 Soporte:

Si tienes problemas, verifica:
1. Que Node.js esté instalado correctamente
2. Que todas las dependencias se hayan instalado sin errores
3. Que no haya errores en el código de la aplicación
