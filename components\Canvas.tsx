
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { type TokenState, type Annotation, type Point, UnitType, AnnotationType, type DrawingState, type Tool, type ViewTransform } from '../types';
import { Token } from './Token';
import { XIcon } from './icons/ToolIcons';

interface CanvasProps {
  mapImage: string | null;
  mapDimensions: { width: number; height: number } | null;
  tokens: TokenState[];
  annotations: Annotation[];
  onDrop: (data: string, position: Point) => void;
  onMouseDown: (point: Point) => void;
  onMouseMove: (point: Point) => void;
  onMouseUp: (canvasRect: DOMRect | undefined, mapRenderInfo: { scale: number, offsetX: number, offsetY: number }) => void;
  onWheel: (e: React.WheelEvent, point: Point) => void;
  drawingState: DrawingState | null;
  activeTool: Tool;
  selectedColor: string;
  paletteColors: string[];
  editingTokenId: number | null;
  onTokenUpdate: (tokenId: number, updates: Partial<TokenState>) => void;
  onFinishEditing: () => void;
  viewTransform: ViewTransform | null;
  onZoomReset: () => void;
  globalTokenSize: number;
}

const hexToRgba = (hex: string, alpha: number): string => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

const SvgOverlay: React.FC<{ 
    annotations: Annotation[], 
    drawingState: DrawingState | null, 
    paths: {path: Point[], isVisible?: boolean}[], 
    selectedColor: string, 
    paletteColors: string[],
    mapToScreen: (point: Point) => Point,
    mapScale: number
}> = ({ annotations, drawingState, paths, selectedColor, paletteColors, mapToScreen, mapScale }) => {
    
    const renderPath = (path: Point[], color: string, strokeDash: string = '5,5') => {
        if (path.length < 2) return null;
        const screenPath = path.map(mapToScreen);
        return <polyline points={screenPath.map(pt => `${pt.x},${pt.y}`).join(' ')} fill="none" stroke={color} strokeWidth="2" strokeDasharray={strokeDash} />
    };

    return (
        <svg className="absolute top-0 left-0 w-full h-full pointer-events-none">
            <defs>
                {paletteColors.map(color => (
                    <marker 
                        key={color} 
                        id={`arrowhead-${color.replace('#','')}`} 
                        markerWidth="10" 
                        markerHeight="7" 
                        refX="8" 
                        refY="3.5" 
                        orient="auto"
                    >
                        <polygon points="0 0, 10 3.5, 0 7" fill={color} />
                    </marker>
                ))}
            </defs>
            
            {paths.map((p, i) => (
                (p.isVisible !== false && p.path.length > 1) && <g key={`path-${i}`}>{renderPath(p.path, "#fca5a5")}</g>
            ))}

            {annotations.filter(a => a.isVisible !== false).map(a => {
                if (a.type === AnnotationType.Arrow) {
                    const screenStart = mapToScreen(a.start);
                    const screenControl = mapToScreen(a.control);
                    const screenEnd = mapToScreen(a.end);
                    const pathData = `M ${screenStart.x} ${screenStart.y} Q ${screenControl.x} ${screenControl.y} ${screenEnd.x} ${screenEnd.y}`;
                    return (
                        <g key={a.id}>
                            <path d={pathData} stroke={a.color} strokeWidth="3" fill="none" markerEnd={`url(#arrowhead-${a.color.replace('#','')})`} />
                            <circle r="4" fill={a.color} stroke="white" strokeWidth="1">
                                <animateMotion dur="2.5s" repeatCount="indefinite" path={pathData} />
                            </circle>
                        </g>
                    );
                }
                if (a.type === AnnotationType.Circle) {
                    const screenCenter = mapToScreen(a.center);
                    const screenRadius = a.radius * mapScale;
                    return (
                        <circle
                            key={a.id}
                            cx={screenCenter.x}
                            cy={screenCenter.y}
                            r={screenRadius}
                            fill={hexToRgba(a.color, 0.3)}
                            stroke={a.color}
                            strokeWidth="2"
                        >
                           <animate 
                                attributeName="r"
                                values={`${screenRadius}; ${screenRadius * 0.85}; ${screenRadius}`}
                                dur="1.5s"
                                repeatCount="indefinite"
                                calcMode="spline"
                                keyTimes="0; 0.5; 1"
                                keySplines="0.42 0 0.58 1; 0.42 0 0.58 1"
                            />
                        </circle>
                    );
                }
                return null;
            })}

            {drawingState && (
                <>
                    {drawingState.type === 'arrow' && drawingState.stage === 'defining-end' && <path d={`M ${mapToScreen(drawingState.start).x} ${mapToScreen(drawingState.start).y} L ${mapToScreen(drawingState.current).x} ${mapToScreen(drawingState.current).y}`} stroke={selectedColor} strokeWidth="3" fill="none" strokeDasharray="5,5" markerEnd={`url(#arrowhead-${selectedColor.replace('#','')})`} />}
                    {drawingState.type === 'arrow' && drawingState.stage === 'defining-control' && <path d={`M ${mapToScreen(drawingState.start).x} ${mapToScreen(drawingState.start).y} Q ${mapToScreen(drawingState.current).x} ${mapToScreen(drawingState.current).y} ${mapToScreen(drawingState.end).x} ${mapToScreen(drawingState.end).y}`} stroke={selectedColor} strokeWidth="3" fill="none" strokeDasharray="5,5" markerEnd={`url(#arrowhead-${selectedColor.replace('#','')})`} />}
                    {drawingState.type === 'circle' && <circle cx={mapToScreen(drawingState.start).x} cy={mapToScreen(drawingState.start).y} r={Math.hypot(drawingState.current.x - drawingState.start.x, drawingState.current.y - drawingState.start.y) * mapScale} fill={hexToRgba(selectedColor, 0.2)} stroke={selectedColor} strokeWidth="2" strokeDasharray="5,5" />}
                    {drawingState.type === 'path' && renderPath([drawingState.start, drawingState.current], '#fca5a5')}
                </>
            )}
        </svg>
    );
};


export const Canvas: React.FC<CanvasProps> = ({ 
    mapImage, mapDimensions, tokens, annotations, onDrop, onMouseDown, onMouseMove, onMouseUp, onWheel, 
    drawingState, activeTool, selectedColor, editingTokenId, onTokenUpdate, onFinishEditing, paletteColors,
    viewTransform, onZoomReset, globalTokenSize
}) => {
    const canvasRef = useRef<HTMLDivElement>(null);
    const textInputRef = useRef<HTMLInputElement>(null);
    const [mapRenderInfo, setMapRenderInfo] = useState({ scale: 1, offsetX: 0, offsetY: 0 });

    useEffect(() => {
        const canvasElement = canvasRef.current;
        if (!canvasElement || !mapDimensions) return;
    
        const calculateRenderInfo = () => {
            const { width: canvasWidth, height: canvasHeight } = canvasElement.getBoundingClientRect();
            const { width: mapWidth, height: mapHeight } = mapDimensions;
    
            const mapRatio = mapWidth / mapHeight;
            const canvasRatio = canvasWidth / canvasHeight;
    
            let scale: number, offsetX: number, offsetY: number;
    
            if (mapRatio > canvasRatio) {
                scale = canvasWidth / mapWidth;
                offsetX = 0;
                offsetY = (canvasHeight - mapHeight * scale) / 2;
            } else {
                scale = canvasHeight / mapHeight;
                offsetX = (canvasWidth - mapWidth * scale) / 2;
                offsetY = 0;
            }
            setMapRenderInfo({ scale, offsetX, offsetY });
        };
    
        calculateRenderInfo(); // Initial calculation
    
        const observer = new ResizeObserver(calculateRenderInfo);
        observer.observe(canvasElement);
    
        return () => observer.disconnect();
    }, [mapDimensions]);
    
    const mapToWrapper = useCallback((point: Point): Point => {
        const { scale, offsetX, offsetY } = mapRenderInfo;
        return {
            x: point.x * scale + offsetX,
            y: point.y * scale + offsetY,
        };
    }, [mapRenderInfo]);

    const wrapperToMap = useCallback((point: Point): Point => {
        const { scale, offsetX, offsetY } = mapRenderInfo;
        if (scale === 0) return { x: 0, y: 0 }; // Avoid division by zero
        return {
            x: (point.x - offsetX) / scale,
            y: (point.y - offsetY) / scale,
        };
    }, [mapRenderInfo]);
    
    const screenToMapPoint = useCallback((screenPoint: Point): Point => {
        let p = { ...screenPoint };
        if (viewTransform) {
            p.x = (p.x - viewTransform.translateX) / viewTransform.scale;
            p.y = (p.y - viewTransform.translateY) / viewTransform.scale;
        }
        return wrapperToMap(p);
    }, [viewTransform, wrapperToMap]);

    const getPointInCanvas = (e: React.MouseEvent | React.WheelEvent | React.DragEvent): Point => {
        if (!canvasRef.current) return { x: 0, y: 0 };
        const rect = canvasRef.current.getBoundingClientRect();
        return { x: e.clientX - rect.left, y: e.clientY - rect.top };
    };

    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    const handleDrop = (e: React.DragEvent) => {
        e.preventDefault();
        const data = e.dataTransfer.getData('application/json');
        if (data && mapDimensions) {
            const screenPoint = getPointInCanvas(e);
            const mapPoint = screenToMapPoint(screenPoint);
            onDrop(data, mapPoint);
        }
    };
  
    const handleMouseEvent = (handler: (mapPoint: Point) => void) => (e: React.MouseEvent) => {
        if (!mapDimensions) return;
        const screenPoint = getPointInCanvas(e);
        const mapPoint = screenToMapPoint(screenPoint);
        handler(mapPoint);
    };
    
    const handleMouseUpEvent = () => {
        if (!mapDimensions) return;
        onMouseUp(canvasRef.current?.getBoundingClientRect(), mapRenderInfo);
    }

    const handleWheelEvent = (e: React.WheelEvent) => {
        if (!mapDimensions) return;
        const screenPoint = getPointInCanvas(e);
        const mapPoint = screenToMapPoint(screenPoint);
        onWheel(e, mapPoint);
    };

    const getCursor = () => {
        if (drawingState) {
            if (drawingState.type === 'moving' || drawingState.type === 'cloning') return 'cursor-grabbing';
            if (drawingState.type === 'arrow' && drawingState.stage === 'defining-control') return 'cursor-grab';
            if (drawingState.type === 'enlarging') return 'cursor-ns-resize';
        }
        if (activeTool === 'move') return 'cursor-grab';
        if (activeTool === 'clone') return 'cursor-copy';
        if (activeTool === 'zoom') return 'cursor-crosshair';
        if (activeTool === 'enlarge') return 'cursor-ns-resize';
        if (activeTool === 'eraser' || activeTool === 'arrow' || activeTool === 'circle' || activeTool === 'path') return 'cursor-crosshair';
        return 'cursor-default';
    }

    const editingToken = tokens.find(t => t.id === editingTokenId);
    
    useEffect(() => {
      if (editingTokenId && textInputRef.current) {
          textInputRef.current.focus();
          textInputRef.current.value = tokens.find(t => t.id === editingTokenId)?.text ?? '';
      }
    }, [editingTokenId, tokens]);

    const mapPointToFinalScreen = useCallback((mapPoint: Point): Point => {
        let p = mapToWrapper(mapPoint);
        if (viewTransform) {
            p.x = p.x * viewTransform.scale + viewTransform.translateX;
            p.y = p.y * viewTransform.scale + viewTransform.translateY;
        }
        return p;
    }, [mapToWrapper, viewTransform]);

    const editingTokenScreenPos = editingToken ? mapPointToFinalScreen(editingToken.position) : null;
    const editingTokenScreenSize = editingToken ? (editingToken.size ?? 1) * 16 * mapRenderInfo.scale * (viewTransform?.scale ?? 1) : 16;


    return (
        <div
            ref={canvasRef}
            className={`w-full h-full bg-gray-700 relative overflow-hidden select-none ${getCursor()}`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onMouseDown={handleMouseEvent(onMouseDown)}
            onMouseMove={handleMouseEvent(onMouseMove)}
            onMouseUp={handleMouseUpEvent}
            onWheel={handleWheelEvent}
        >
            <div
              className="absolute top-0 left-0 w-full h-full"
              style={{
                transform: viewTransform ? `translate(${viewTransform.translateX}px, ${viewTransform.translateY}px) scale(${viewTransform.scale})` : 'none',
                transition: 'transform 0.5s cubic-bezier(0.25, 1, 0.5, 1)',
                transformOrigin: 'top left',
              }}
            >
              {mapImage && (
                  <div 
                      className="absolute top-0 left-0 pointer-events-none"
                      style={{
                          width: `${mapRenderInfo.scale * (mapDimensions?.width ?? 0)}px`,
                          height: `${mapRenderInfo.scale * (mapDimensions?.height ?? 0)}px`,
                          left: `${mapRenderInfo.offsetX}px`,
                          top: `${mapRenderInfo.offsetY}px`,
                          backgroundImage: `url(${mapImage})`,
                          backgroundSize: '100% 100%',
                      }}
                  />
              )}
              {!mapImage && !viewTransform && (
                  <div className="flex items-center justify-center h-full">
                      <p className="text-2xl text-gray-400">Upload a map to begin your strategy</p>
                  </div>
              )}
              
              {mapDimensions && <SvgOverlay annotations={annotations} drawingState={drawingState} paths={tokens.map(t => ({path: t.path, isVisible: t.isVisible}))} selectedColor={selectedColor} paletteColors={paletteColors} mapToScreen={mapToWrapper} mapScale={mapRenderInfo.scale} />}

              {mapDimensions && tokens.filter(t => t.isVisible !== false).map(token => (
                  <Token key={token.id} token={token} mapToScreen={mapToWrapper} mapScale={mapRenderInfo.scale} globalTokenSize={globalTokenSize} />
              ))}
              
              {mapDimensions && drawingState?.type === 'cloning' && (
                  <div style={{ opacity: 0.6 }}>
                      <Token
                          key="clone-ghost"
                          token={{ ...drawingState.tokenToClone, position: drawingState.current }}
                          mapToScreen={mapToWrapper}
                          mapScale={mapRenderInfo.scale}
                          globalTokenSize={globalTokenSize}
                      />
                  </div>
              )}

            </div>
            
            {drawingState?.type === 'zoom' && (() => {
                const start = mapPointToFinalScreen(drawingState.start);
                const current = mapPointToFinalScreen(drawingState.current);
                const x = Math.min(start.x, current.x);
                const y = Math.min(start.y, current.y);
                const width = Math.abs(start.x - current.x);
                const height = Math.abs(start.y - current.y);
                return <div className="absolute border-2 border-dashed border-blue-400 bg-blue-400/20 pointer-events-none" style={{ left: x, top: y, width, height }} />;
            })()}

            {viewTransform && (
                <button onClick={onZoomReset} title="Reset Zoom (Esc)" className="absolute top-4 right-4 bg-gray-800/80 p-2 rounded-full text-white hover:bg-gray-700 transition-colors z-50">
                    <div className="w-6 h-6"><XIcon /></div>
                </button>
            )}

            {editingToken && editingTokenScreenPos && (
                <input
                    ref={textInputRef}
                    type="text"
                    defaultValue={editingToken.text}
                    onChange={(e) => onTokenUpdate(editingToken.id, { text: e.target.value })}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === 'Escape') {
                            onFinishEditing();
                            e.currentTarget.blur();
                        }
                    }}
                    onBlur={() => onFinishEditing()}
                    style={{
                        position: 'absolute',
                        left: editingTokenScreenPos.x,
                        top: editingTokenScreenPos.y,
                        transform: 'translate(-50%, -50%)',
                        fontSize: `${editingTokenScreenSize}px`,
                        fontFamily: editingToken.fontFamily,
                        color: editingToken.color,
                        lineHeight: '1.2',
                    }}
                    className="bg-black bg-opacity-80 px-3 py-1 rounded-md shadow-lg outline-none border border-blue-500 focus:ring-2 focus:ring-blue-400 z-50 font-bold"
                    autoFocus
                />
            )}
        </div>
    );
};
