import React from 'react';
import { type TokenState, UnitType, ArtilleryType, type Point } from '../types';
import { 
    SwordIcon, ShieldIcon, PikeIcon, ArcherIcon, CavalryIcon,
    CannonIcon, MetrallaIcon, MortarIcon, BallistaIcon, RocketLauncherIcon, SiegeTowerIcon, TrebuchetIcon, CulverinIcon
} from './icons/UnitIcons';

interface TokenProps {
  token: TokenState;
  mapToScreen: (point: Point) => Point;
  mapScale: number;
  globalTokenSize: number;
}

const unitIcons: Record<string, React.ReactNode> = {
  [UnitType.Sword]: <SwordIcon />,
  [UnitType.Shield]: <ShieldIcon />,
  [UnitType.Pike]: <PikeIcon />,
  [UnitType.Archer]: <ArcherIcon />,
  [UnitType.Cavalry]: <CavalryIcon />,
};

const artilleryIcons: Record<ArtilleryType, React.ReactNode> = {
  [ArtilleryType.Cannon]: <CannonIcon />,
  [ArtilleryType.Metralla]: <MetrallaIcon />,
  [ArtilleryType.Mortar]: <MortarIcon />,
  [ArtilleryType.Ballista]: <BallistaIcon />,
  [ArtilleryType.RocketLauncher]: <RocketLauncherIcon />,
  [ArtilleryType.SiegeTower]: <SiegeTowerIcon />,
  [ArtilleryType.Trebuchet]: <TrebuchetIcon />,
  [ArtilleryType.Culverin]: <CulverinIcon />,
};


const getLighterColor = (hex: string) => {
    if (hex.startsWith('#')) hex = hex.slice(1);
    const bigint = parseInt(hex, 16);
    let r = (bigint >> 16) & 255;
    let g = (bigint >> 8) & 255;
    let b = bigint & 255;
    r = Math.min(255, r + 40);
    g = Math.min(255, g + 40);
    b = Math.min(255, b + 40);
    return `#${(1 << 24 | r << 16 | g << 8 | b).toString(16).slice(1)}`;
};

const BASE_TOKEN_SIZE_ON_MAP = 40; // Base size in original map pixels
const BASE_FONT_SIZE = 1; // Base rem size

export const Token: React.FC<TokenProps> = ({ token, mapToScreen, mapScale, globalTokenSize }) => {
  const { position, type, color, number, size = 1, text, fontFamily, artilleryType, rotation = 0, customImage } = token;

  const screenPosition = mapToScreen(position);
  const globalScale = 1 + (globalTokenSize / 100);
  const finalSizeMultiplier = size * globalScale;

  if (type === UnitType.Text) {
    // For text, 'size' is a multiplier for the base font size.
    const renderedFontSize = finalSizeMultiplier * BASE_FONT_SIZE * 16 * mapScale; 
    return (
      <div
        className="absolute transition-transform duration-100 ease-linear pointer-events-none"
        style={{
          left: `${screenPosition.x}px`,
          top: `${screenPosition.y}px`,
          transform: `translate(-50%, -50%) rotate(${rotation}deg)`,
          pointerEvents: 'none',
        }}
      >
        <span
          className="bg-black bg-opacity-70 font-bold px-3 py-1 rounded-md shadow-lg whitespace-nowrap"
          style={{
            fontSize: `${renderedFontSize}px`,
            fontFamily: fontFamily,
            color: color,
            display: 'inline-block',
          }}
        >
          {text}
        </span>
      </div>
    );
  }

  const renderedSize = BASE_TOKEN_SIZE_ON_MAP * mapScale;
  const gradientId = `grad-${color.replace('#', '')}`;
  const lighterColor = getLighterColor(color);
  const isCustom = type === UnitType.Custom || type === UnitType.CustomArtillery;

  let iconContent: React.ReactNode = null;
  if (type === UnitType.Artillery && artilleryType) {
      iconContent = artilleryIcons[artilleryType];
  } else if (unitIcons[type]) {
      iconContent = unitIcons[type];
  }

  return (
    <div
      className="absolute transition-transform duration-100 ease-linear pointer-events-none"
      style={{
        width: `${renderedSize}px`,
        height: `${renderedSize}px`,
        left: `${screenPosition.x}px`,
        top: `${screenPosition.y}px`,
        transform: `translate(-50%, -50%) scale(${finalSizeMultiplier}) rotate(${rotation}deg)`,
      }}
    >
      <svg viewBox="0 0 100 100" className="w-full h-full drop-shadow-lg">
        <defs>
          <radialGradient id={gradientId} cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
            <stop offset="0%" style={{ stopColor: lighterColor, stopOpacity: 1 }} />
            <stop offset="100%" style={{ stopColor: color, stopOpacity: 1 }} />
          </radialGradient>
          {isCustom && customImage && (
            <clipPath id={`clip-${token.id}`}>
              <circle cx="50" cy="50" r="42" />
            </clipPath>
          )}
        </defs>
        <circle cx="50" cy="50" r="45" fill={`url(#${gradientId})`} stroke="#000" strokeWidth="3" />
        
        {isCustom && customImage ? (
          <image href={customImage} x="0" y="0" height="100" width="100" clipPath={`url(#clip-${token.id})`} />
        ) : (
          <g transform="translate(15, 15) scale(0.7, 0.7)" className="text-white">
            {iconContent}
          </g>
        )}

        {number !== null && (
          <text
            x="50"
            y="55"
            textAnchor="middle"
            dominantBaseline="middle"
            stroke="black"
            strokeWidth="3"
            paintOrder="stroke"
            fontSize="40"
            fontWeight="bold"
            fontFamily="sans-serif"
            className="number-color-anim"
          >
            {number}
          </text>
        )}
      </svg>
    </div>
  );
};