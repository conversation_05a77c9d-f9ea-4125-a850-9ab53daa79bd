{"name": "battle-strategy-creator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "main": "electron/main.js", "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0", "electron": "^32.0.0", "electron-builder": "^25.0.0", "concurrently": "^9.0.0", "wait-on": "^8.0.0"}, "build": {"appId": "com.battlestrategy.creator", "productName": "Battle Strategy Creator", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "package.json"], "win": {"target": "nsis", "icon": "electron/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}