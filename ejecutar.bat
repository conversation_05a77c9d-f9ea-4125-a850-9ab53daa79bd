@echo off
echo ========================================
echo  Battle Strategy Creator - Ejecutar
echo ========================================
echo.

REM Verificar si Node.js está instalado
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js no está instalado.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
)

REM Verificar si las dependencias están instaladas
if not exist "node_modules" (
    echo ❌ Dependencias no instaladas.
    echo Ejecuta primero: instalar.bat
    echo.
    pause
    exit /b 1
)

REM Verificar configuración
if not exist ".env.local" (
    echo ⚠️  Archivo .env.local no encontrado.
    echo Creando archivo de configuración básico...
    echo # Configuración de la aplicación > .env.local
    echo # Agrega tu clave de API de Gemini aquí: >> .env.local
    echo # GEMINI_API_KEY=tu_clave_aqui >> .env.local
    echo.
    echo 📝 IMPORTANTE: Edita el archivo .env.local y agrega tu GEMINI_API_KEY
    echo.
)

echo ✅ Iniciando el servidor de desarrollo...
echo.
echo 🌐 La aplicación se abrirá en: http://localhost:5173
echo.
echo Para detener el servidor, presiona Ctrl+C
echo.
echo ========================================
echo.

REM Ejecutar el proyecto
npm run dev

echo.
echo ========================================
echo  Servidor detenido
echo ========================================
pause
