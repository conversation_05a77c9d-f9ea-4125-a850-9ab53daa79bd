

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Toolbar } from './components/Toolbar';
import { Canvas } from './components/Canvas';
import { LayersPanel } from './components/LayersPanel';
import {
  type TokenState,
  type Annotation,
  type Tool,
  type Point,
  UnitType,
  AnnotationType,
  type DrawingState,
  type TextPreset,
  ArtilleryType,
  type CustomUnit,
  type ProjectData,
  type ViewTransform,
  type LayerEntry,
  type LayerItemIdentifier,
  type LayerGroup,
} from './types';
import { INITIAL_PALETTE_COLORS, FONT_FACES } from './constants';

const getPathLength = (path: Point[]): number => {
    if (path.length < 2) return 0;
    return path.slice(1).reduce((acc, point, i) => acc + Math.hypot(point.x - path[i].x, point.y - path[i].y), 0);
};

const getPointOnPath = (path: Point[], progress: number): Point => {
    if (path.length < 2) return path[0] || { x: 0, y: 0 };
    const totalLength = getPathLength(path);
    if(totalLength === 0) return path[0];

    let distanceTraveled = progress * totalLength;
    
    for (let i = 0; i < path.length - 1; i++) {
        const p1 = path[i];
        const p2 = path[i+1];
        const segmentLength = Math.hypot(p2.x - p1.x, p2.y - p1.y);
        
        if (distanceTraveled <= segmentLength) {
            const segmentProgress = segmentLength === 0 ? 0 : distanceTraveled / segmentLength;
            return {
                x: p1.x + (p2.x - p1.x) * segmentProgress,
                y: p1.y + (p2.y - p1.y) * segmentProgress
            };
        }
        distanceTraveled -= segmentLength;
    }
    return path[path.length - 1];
};

const distSq = (p1: Point, p2: Point) => (p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2;

const distToSegmentSq = (p: Point, v: Point, w: Point) => {
    const l2 = distSq(v, w);
    if (l2 === 0) return distSq(p, v);
    let t = ((p.x - v.x) * (w.x - v.x) + (p.y - v.y) * (w.y - v.y)) / l2;
    t = Math.max(0, Math.min(1, t));
    const projection = { x: v.x + t * (w.x - v.x), y: v.y + t * (w.y - v.y) };
    return distSq(p, projection);
};

interface NewTextState {
    content: string;
    size: number; // in rem
    fontFamily: string;
    outlineColor1: string;
    outlineColor2: string;
    outlineWidth: number;
}

interface HistoryState {
    tokens: TokenState[];
    annotations: Annotation[];
    layers: LayerEntry[];
}

const toolShortcuts: { [key: string]: Tool } = {
  s: 'select',
  m: 'move',
  c: 'clone',
  z: 'zoom',
  e: 'enlarge',
  t: 'text',
  b: 'eraser',
};


const App: React.FC = () => {
  const [mapImage, setMapImage] = useState<string | null>(null);
  const [mapDimensions, setMapDimensions] = useState<{ width: number; height: number; } | null>(null);
  const [tokens, setTokens] = useState<TokenState[]>([]);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [layers, setLayers] = useState<LayerEntry[]>([]);
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [redoStack, setRedoStack] = useState<HistoryState[]>([]);
  const [viewTransform, setViewTransform] = useState<ViewTransform | null>(null);

  const [activeTool, setActiveTool] = useState<Tool>('select');
  const [selectedTokenId, setSelectedTokenId] = useState<number | null>(null);
  const [drawingState, setDrawingState] = useState<DrawingState | null>(null);
  const [paletteColors, setPaletteColors] = useState<string[]>(INITIAL_PALETTE_COLORS);
  const [selectedColor, setSelectedColor] = useState<string>(INITIAL_PALETTE_COLORS[0]);
  const [editingTokenId, setEditingTokenId] = useState<number | null>(null);
  const [newText, setNewText] = useState<NewTextState>({
    content: 'Draggable Text',
    size: 1.5, // rem
    fontFamily: FONT_FACES[0],
    outlineColor1: '#FFFFFF',
    outlineColor2: '#000080',
    outlineWidth: 2,
  });
  const [textPresets, setTextPresets] = useState<TextPreset[]>(() => {
    try {
      const saved = localStorage.getItem('textPresets');
      return saved ? JSON.parse(saved) : [];
    } catch (error) {
      console.error('Failed to load text presets from localStorage', error);
      return [];
    }
  });
  const [customUnits, setCustomUnits] = useState<CustomUnit[]>([]);
  const [customArtillery, setCustomArtillery] = useState<CustomUnit[]>([]);
  const [globalTokenSize, setGlobalTokenSize] = useState<number>(0);


  const nextId = useRef(1);
  const animationFrameId = useRef<number | null>(null);
  const lastClick = useRef<{ time: number; tokenId: number | null }>({ time: 0, tokenId: null });
  const canvasRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    try {
      localStorage.setItem('textPresets', JSON.stringify(textPresets));
    } catch (error) {
      console.error('Failed to save text presets to localStorage', error);
    }
  }, [textPresets]);

  const getNextId = useCallback(() => {
    nextId.current += 1;
    return nextId.current;
  }, []);

  const saveStateToHistory = useCallback(() => {
    setHistory(prev => [...prev, { tokens, annotations, layers }]);
    setRedoStack([]);
  }, [tokens, annotations, layers]);

  const handleUndo = useCallback(() => {
    if (history.length === 0) return;

    const lastState = history[history.length - 1];
    const newHistory = history.slice(0, history.length - 1);

    setRedoStack(prev => [...prev, { tokens, annotations, layers }]);
    
    setTokens(lastState.tokens);
    setAnnotations(lastState.annotations);
    setLayers(lastState.layers);
    setHistory(newHistory);

    setSelectedTokenId(null);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [history, tokens, annotations, layers]);

  const handleRedo = useCallback(() => {
    if (redoStack.length === 0) return;

    const nextState = redoStack[redoStack.length - 1];
    const newRedoStack = redoStack.slice(0, redoStack.length - 1);

    setHistory(prev => [...prev, { tokens, annotations, layers }]);

    setTokens(nextState.tokens);
    setAnnotations(nextState.annotations);
    setLayers(nextState.layers);
    setRedoStack(newRedoStack);

    setSelectedTokenId(null);
    setDrawingState(null);
    setEditingTokenId(null);
    setActiveTool('select');
  }, [redoStack, tokens, annotations, layers]);

  useEffect(() => {
    setDrawingState(null);
  }, [activeTool]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const target = e.target as HTMLElement;
      const isEditing = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;

      if (e.key === 'Escape') {
        setDrawingState(null);
        setEditingTokenId(null);
        setViewTransform(null);
        return;
      }

      if (isEditing) return;

      const key = e.key.toLowerCase();
      const isUndo = (e.ctrlKey || e.metaKey) && key === 'z' && !e.shiftKey;
      const isRedo = (e.ctrlKey || e.metaKey) && (key === 'y' || (key === 'z' && e.shiftKey));

      if (isUndo) {
        e.preventDefault();
        handleUndo();
      } else if (isRedo) {
        e.preventDefault();
        handleRedo();
      } else if (toolShortcuts[key]) {
        e.preventDefault();
        setActiveTool(toolShortcuts[key]);
      }
    };
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleUndo, handleRedo]);

  const handleMapUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        if (!imageUrl) return;

        const img = new Image();
        img.onload = () => {
          saveStateToHistory();
          setMapImage(imageUrl);
          setMapDimensions({ width: img.naturalWidth, height: img.naturalHeight });
          setTokens([]);
          setAnnotations([]);
          setLayers([]);
          setSelectedTokenId(null);
          setDrawingState(null);
        };
        img.src = imageUrl;
      };
      reader.readAsDataURL(event.target.files[0]);
    }
  };

  const addToken = (type: UnitType, position: Point, options: Partial<TokenState> = {}) => {
    saveStateToHistory();
    const newId = getNextId();
    const newToken: TokenState = {
      id: newId,
      type,
      color: options.color || selectedColor,
      number: null,
      position,
      path: [],
      animationProgress: 0,
      patrolForward: true,
      size: options.size ?? 1,
      rotation: options.rotation ?? 0,
      text: type === UnitType.Text ? options.text ?? 'Text' : undefined,
      fontFamily: type === UnitType.Text ? options.fontFamily : undefined,
      outlineColor1: type === UnitType.Text ? options.outlineColor1 : undefined,
      outlineColor2: type === UnitType.Text ? options.outlineColor2 : undefined,
      outlineWidth: type === UnitType.Text ? options.outlineWidth : undefined,
      artilleryType: type === UnitType.Artillery ? options.artilleryType : undefined,
      customImage: type === UnitType.Custom || type === UnitType.CustomArtillery ? options.customImage : undefined,
      isVisible: true,
      animationSpeed: options.animationSpeed ?? 1.0,
      isPatrol: options.isPatrol ?? false,
      name: options.name,
    };
    setTokens((prev) => [...prev, newToken]);
    setLayers(prev => [{id: newId, type: 'token'}, ...prev]);
    return newToken;
  }

  const handleTokenDrop = (data: string, position: Point) => {
    if (!mapDimensions) return; // Cannot drop tokens if there's no map
    let parsedData;
    try {
        parsedData = JSON.parse(data);
    } catch (e) {
        console.error("Failed to parse dropped data:", e);
        return;
    }

    if (parsedData.isTextToken) {
        addToken(UnitType.Text, position, {
            text: parsedData.content,
            size: parsedData.size,
            fontFamily: parsedData.fontFamily,
            color: selectedColor,
            outlineColor1: parsedData.outlineColor1,
            outlineColor2: parsedData.outlineColor2,
            outlineWidth: parsedData.outlineWidth,
        });
    } else if (parsedData.type === UnitType.Custom) {
        addToken(UnitType.Custom, position, {
            customImage: parsedData.imageData,
            name: parsedData.name,
            color: selectedColor,
        });
    } else if (parsedData.type === UnitType.CustomArtillery) {
        addToken(UnitType.CustomArtillery, position, {
            customImage: parsedData.imageData,
            name: parsedData.name,
            color: selectedColor,
        });
    } else if (parsedData.type === UnitType.Artillery) {
        addToken(UnitType.Artillery, position, {
            artilleryType: parsedData.artilleryType,
            color: selectedColor,
        });
    } else if (Object.values(UnitType).includes(parsedData.type)) {
        addToken(parsedData.type, position);
    }
  };

  const deleteItem = useCallback((id: number, type: 'token' | 'annotation') => {
    saveStateToHistory();
    if (type === 'token') {
        setTokens(prev => prev.filter(t => t.id !== id));
        if (selectedTokenId === id) setSelectedTokenId(null);
        if (editingTokenId === id) setEditingTokenId(null);
    } else {
        setAnnotations(prev => prev.filter(a => a.id !== id));
    }

    const removeItemRec = (entries: LayerEntry[]): LayerEntry[] => {
      return entries.reduce((acc, entry) => {
          if (entry.type === 'group') {
              const newItems = removeItemRec(entry.items || []); // Guard against undefined items
              if (newItems.length > 0) {
                   acc.push({ ...entry, items: newItems });
              }
          } else if (!(entry.id === id && entry.type === type)) {
              acc.push(entry);
          }
          return acc;
      }, [] as LayerEntry[]);
    };

    setLayers(prev => removeItemRec(prev));

  }, [selectedTokenId, editingTokenId, saveStateToHistory]);

  const handleZoom = useCallback((p1: Point, p2: Point, canvasRect: DOMRect | undefined, mapRenderInfo: { scale: number, offsetX: number, offsetY: number }) => {
    if (!mapDimensions || !canvasRect) return;

    const { scale: mapScale, offsetX: mapOffsetX, offsetY: mapOffsetY } = mapRenderInfo;

    const wrapperP1 = { x: p1.x * mapScale + mapOffsetX, y: p1.y * mapScale + mapOffsetY };
    const wrapperP2 = { x: p2.x * mapScale + mapOffsetX, y: p2.y * mapScale + mapOffsetY };

    const zoomRectWidth = Math.abs(wrapperP1.x - wrapperP2.x);
    const zoomRectHeight = Math.abs(wrapperP1.y - wrapperP2.y);
    
    const scaleX = canvasRect.width / zoomRectWidth;
    const scaleY = canvasRect.height / zoomRectHeight;
    const scale = Math.min(scaleX, scaleY, 8); // Cap max zoom at 8x

    const zoomCenterX = Math.min(wrapperP1.x, wrapperP2.x) + zoomRectWidth / 2;
    const zoomCenterY = Math.min(wrapperP1.y, wrapperP2.y) + zoomRectHeight / 2;

    const translateX = (canvasRect.width / 2) - (zoomCenterX * scale);
    const translateY = (canvasRect.height / 2) - (zoomCenterY * scale);

    setViewTransform({ scale, translateX, translateY });

  }, [mapDimensions]);

  const handleZoomReset = useCallback(() => {
    setViewTransform(null);
  }, []);

  const handleMouseDown = (point: Point) => {
    if (!mapDimensions) return;
    
    const TOKEN_HIT_RADIUS = 20;
    
    if (activeTool === 'enlarge') {
        const tokenToEnlarge = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToEnlarge) {
            saveStateToHistory();
            setDrawingState({
                type: 'enlarging',
                tokenId: tokenToEnlarge.id,
                startY: point.y,
                startSize: tokenToEnlarge.size ?? 1,
                current: point
            });
        }
        return;
    }

    if (activeTool === 'zoom') {
        setDrawingState({ type: 'zoom', start: point, current: point });
        return;
    }

    if (activeTool === 'move' || activeTool === 'clone') {
        const tokenToInteract = [...tokens].reverse().find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        if (tokenToInteract) {
            if (activeTool === 'move') {
                saveStateToHistory();
                const offset = { x: point.x - tokenToInteract.position.x, y: point.y - tokenToInteract.position.y };
                setDrawingState({ type: 'moving', tokenId: tokenToInteract.id, offset, current: point });
            } else { // clone
                setDrawingState({ type: 'cloning', tokenToClone: tokenToInteract, current: point });
            }
        }
        return;
    }

    if (activeTool === 'eraser') {
      const tokenToDelete = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
      if (tokenToDelete) {
        deleteItem(tokenToDelete.id, 'token');
        return;
      }
      
      const annotationToDelete = annotations.find(a => {
        if (a.isVisible === false) return false;
        if (a.type === AnnotationType.Circle) return distSq(point, a.center) <= a.radius * a.radius;
        if (a.type === AnnotationType.Arrow) {
            const HIT_RADIUS_SQ = (TOKEN_HIT_RADIUS/2) ** 2;
            return distToSegmentSq(point, a.start, a.control) < HIT_RADIUS_SQ ||
                   distToSegmentSq(point, a.control, a.end) < HIT_RADIUS_SQ;
        }
        return false;
      });

      if (annotationToDelete) {
        deleteItem(annotationToDelete.id, 'annotation');
        return;
      }

       const tokenWithPath = tokens.find(token => {
        if (!token.path || token.path.length < 2 || token.isVisible === false) return false;
        for (let i = 0; i < token.path.length - 1; i++) {
          if (distToSegmentSq(point, token.path[i], token.path[i+1]) < (TOKEN_HIT_RADIUS/2)**2) return true;
        }
        return false;
      });

      if (tokenWithPath) clearPath(tokenWithPath.id);
      return;
    }

    if (activeTool === 'select') {
        const clickedToken = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
        
        if (clickedToken) {
            const now = Date.now();
            const isDoubleClick = clickedToken.type === UnitType.Text && 
                                  now - lastClick.current.time < 300 && 
                                  lastClick.current.tokenId === clickedToken.id;
    
            if (isDoubleClick) {
                // Double-click to edit text
                lastClick.current = { time: 0, tokenId: null }; // Reset click tracker
                setEditingTokenId(clickedToken.id);
                setSelectedTokenId(null); // Deselect to avoid confusion with path drawing
                setActiveTool('select'); // Ensure tool remains 'select' for editing.
            } else {
                // Single-click to select any token for path drawing
                lastClick.current = { time: now, tokenId: clickedToken.id };
                setSelectedTokenId(clickedToken.id);
                setEditingTokenId(null);
                setActiveTool('path');
            }
        } else {
            // Clicked on empty space
            setSelectedTokenId(null);
            setEditingTokenId(null);
        }
        return;
    }
    
    if (activeTool === 'arrow') {
        if (drawingState?.type === 'arrow' && drawingState.stage === 'defining-control') {
            saveStateToHistory();
            const { start, end, current } = drawingState;
            const newId = getNextId();
            setAnnotations(prev => [...prev, { id: newId, type: AnnotationType.Arrow, start, end, control: current, color: selectedColor, isVisible: true }]);
            setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
            setDrawingState(null);
        } else {
            setDrawingState({ type: 'arrow', stage: 'defining-end', start: point, current: point });
        }
    } else if (activeTool === 'path' && selectedTokenId !== null) {
        const token = tokens.find(t => t.id === selectedTokenId);
        if (!token) return;
        const startPoint = token.path.length > 0 ? token.path[token.path.length-1] : token.position;
        setDrawingState({ type: 'path', start: startPoint, current: point, pathForTokenId: selectedTokenId });
    } else if (activeTool === 'circle') {
        setDrawingState({ type: 'circle', start: point, current: point });
    }
  };

  const handleMouseMove = (point: Point) => {
    if (!drawingState) return;

    if (drawingState.type === 'enlarging') {
        const { tokenId, startY, startSize } = drawingState;
        const deltaY = point.y - startY;
        const SENSITIVITY = 0.01;
        const newSize = Math.max(0.2, Math.min(3.0, startSize - deltaY * SENSITIVITY));
        setTokens(currentTokens =>
            currentTokens.map(t => (t.id === tokenId ? { ...t, size: newSize } : t))
        );
    } else if (drawingState.type === 'moving') {
        const { tokenId, offset } = drawingState;
        const newPosition = { x: point.x - offset.x, y: point.y - offset.y };
        setTokens(currentTokens =>
            currentTokens.map(t => (t.id === tokenId ? { ...t, position: newPosition } : t))
        );
    }
    
    setDrawingState(prev => (prev ? { ...prev, current: point } : null));
  };
  
  const handleMouseUp = (canvasRect: DOMRect | undefined, mapRenderInfo: { scale: number, offsetX: number, offsetY: number }) => {
    if (!drawingState) return;
    
    if (drawingState.type === 'enlarging') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'moving') {
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'cloning') {
        saveStateToHistory();
        const { tokenToClone, current } = drawingState;
        const { id, path, animationProgress, patrolForward, ...options } = tokenToClone;
        addToken(options.type, current, options);
        setDrawingState(null);
        return;
    }
    
    if (drawingState.type === 'zoom') {
        const { start, current } = drawingState;
        const dx = Math.abs(start.x - current.x);
        const dy = Math.abs(start.y - current.y);
        // Ensure zoom area is not too small
        if (dx > 10 && dy > 10) {
            handleZoom(start, current, canvasRect, mapRenderInfo);
        }
        setDrawingState(null);
        return;
    }

    if (drawingState.type === 'arrow' && drawingState.stage === 'defining-end') {
        if (distSq(drawingState.start, drawingState.current) < 5 * 5) {
            setDrawingState(null);
            return;
        }
        const { start, current } = drawingState;
        const control = { x: (start.x + current.x) / 2, y: (start.y + current.y) / 2 };
        setDrawingState({ type: 'arrow', stage: 'defining-control', start, end: current, current: control });
        return;
    }

    const { type, start, current } = drawingState;
     if (distSq(start, current) < 5 * 5 && type !== 'path') {
        setDrawingState(null);
        return;
    }

    if (type === 'circle') {
        saveStateToHistory();
        const radius = Math.hypot(current.x - start.x, current.y - start.y);
        const newId = getNextId();
        setAnnotations(prev => [...prev, { id: newId, type: AnnotationType.Circle, center: start, radius, color: selectedColor, isVisible: true }]);
        setLayers(prev => [{id: newId, type: 'annotation'}, ...prev]);
    } else if (type === 'path' && drawingState.pathForTokenId !== undefined) {
        saveStateToHistory();
        const { pathForTokenId } = drawingState;
        setTokens(prevTokens => prevTokens.map(token => {
            if (token.id === pathForTokenId) {
                const newPath = token.path.length === 0 ? [token.position, current] : [...token.path, current];
                return { ...token, path: newPath };
            }
            return token;
        }));
    }
    setDrawingState(null);
  };
  
  const handleTokenUpdate = (tokenId: number, updates: Partial<TokenState>) => {
    setTokens(prev => {
        const needsHistory = !Object.keys(updates).every(k => ['animationProgress', 'position', 'patrolForward'].includes(k));
        if(needsHistory) saveStateToHistory();
        return prev.map(t => t.id === tokenId ? { ...t, ...updates } : t)
    });
  }
  
  const handleWheel = (e: React.WheelEvent, point: Point) => {
    if (!mapDimensions) return;
    const TOKEN_HIT_RADIUS = 20;
    const tokenToUpdate = tokens.find(t => t.isVisible !== false && distSq(t.position, point) < (TOKEN_HIT_RADIUS * (t.size ?? 1)) ** 2);
    if (!tokenToUpdate) return;
    
    e.preventDefault();
    
    if (e.ctrlKey) {
       handleTokenUpdate(tokenToUpdate.id, { size: Math.max(0.2, Math.min(3.0, (tokenToUpdate.size ?? 1) - Math.sign(e.deltaY) * 0.1)) });
    } else if(tokenToUpdate.type !== UnitType.Text && tokenToUpdate.type !== UnitType.Artillery) {
      let currentNum = tokenToUpdate.number ?? (e.deltaY < 0 ? 0 : 2);
      if (e.deltaY < 0) {
        currentNum = currentNum >= 15 ? 1 : currentNum + 1;
      } else {
        currentNum = currentNum <= 1 ? 15 : currentNum - 1;
      }
      handleTokenUpdate(tokenToUpdate.id, { number: currentNum });
    }
  };

  const animationLoop = useCallback(() => {
    setTokens(currentTokens =>
      currentTokens.map(token => {
        if (token.path.length < 2 || token.isVisible === false) return token;

        const isPatrol = token.isPatrol ?? false;
        const animationSpeed = token.animationSpeed ?? 1.0;

        const pathLength = getPathLength(token.path);
        if (pathLength === 0) return token;
        
        const baseSpeed = 2; // map units per frame at 1x speed
        const progressIncrement = (baseSpeed * Math.abs(animationSpeed)) / pathLength;

        let newProgress = token.animationProgress;
        let isForward = token.patrolForward ?? true;

        if (isPatrol) {
          if (isForward) {
            newProgress += progressIncrement;
            if (newProgress >= 1.0) {
              newProgress = 1.0;
              isForward = false;
            }
          } else {
            newProgress -= progressIncrement;
            if (newProgress <= 0.0) {
              newProgress = 0.0;
              isForward = true;
            }
          }
        } else {
          // Non-patrol mode: infinite loop from start to end.
          newProgress += progressIncrement;
          if (newProgress >= 1.0) {
            newProgress %= 1.0; // Loop back to the beginning.
          }
        }

        const newPosition = getPointOnPath(token.path, newProgress);

        return { ...token, animationProgress: newProgress, position: newPosition, patrolForward: isForward };
      })
    );
    animationFrameId.current = requestAnimationFrame(animationLoop);
  }, []);

  useEffect(() => {
    animationFrameId.current = requestAnimationFrame(animationLoop);
    return () => {
        if(animationFrameId.current) cancelAnimationFrame(animationFrameId.current);
    };
  }, [animationLoop]);

  const clearPath = (tokenId: number) => {
    saveStateToHistory();
    setTokens(prev => prev.map(t => {
      if (t.id === tokenId) {
        const originalPosition = t.path.length > 0 ? t.path[0] : t.position;
        return { ...t, path: [], animationProgress: 0, position: originalPosition, patrolForward: true };
      }
      return t;
    }));
    if (selectedTokenId === tokenId) {
      setSelectedTokenId(null);
      setActiveTool('select');
    }
  };

  const clearAll = () => {
    saveStateToHistory();
    setTokens([]);
    setAnnotations([]);
    setLayers([]);
    setMapImage(null);
    setMapDimensions(null);
    setSelectedTokenId(null);
    setActiveTool('select');
  }

  const handleAddTextPreset = useCallback(() => {
    if (textPresets.some(p => p.content === newText.content && p.fontFamily === newText.fontFamily && p.size === newText.size)) {
        return;
    }
    const preset: TextPreset = { id: Date.now(), ...newText };
    setTextPresets(prev => [...prev, preset]);
  }, [newText, textPresets]);

  const handleDeleteTextPreset = useCallback((id: number) => {
    setTextPresets(prev => prev.filter(p => p.id !== id));
  }, []);

  const handleLoadTextPreset = useCallback((preset: TextPreset) => {
    setNewText({
        ...newText,
        content: preset.content,
        size: preset.size,
        fontFamily: preset.fontFamily,
    });
  }, [newText]);

  const handlePaletteColorChange = (index: number, newColor: string) => {
      saveStateToHistory();
      const oldColor = paletteColors[index];
      const newPalette = [...paletteColors];
      newPalette[index] = newColor;
      setPaletteColors(newPalette);

      if (selectedColor === oldColor) {
          setSelectedColor(newColor);
      }
  };
  
  const handleToggleVisibility = (id: number, type: 'token' | 'annotation') => {
      saveStateToHistory();
      if (type === 'token') {
          setTokens(prev => prev.map(t => t.id === id ? { ...t, isVisible: !(t.isVisible ?? true) } : t));
      } else {
          setAnnotations(prev => prev.map(a => a.id === id ? { ...a, isVisible: !(a.isVisible ?? true) } : a));
      }
  };

    const handleToggleGroupVisibility = (groupId: number) => {
        saveStateToHistory();

        const findGroupRec = (entries: LayerEntry[], id: number): LayerGroup | null => {
            for (const entry of entries) {
                if (entry.type === 'group') {
                    if (entry.id === id) return entry;
                    // No nested search as per new rules, but leaving for future-proofing
                    // const found = findGroupRec(entry.items, id);
                    // if (found) return found;
                }
            }
            return null;
        };

        const group = findGroupRec(layers, groupId);
        if (!group) return;

        const itemsToUpdate = group.items.map(i => i as LayerItemIdentifier);
        
        const itemMap = new Map<string, TokenState | Annotation>();
        tokens.forEach(t => itemMap.set(`token-${t.id}`, t));
        annotations.forEach(a => itemMap.set(`annotation-${a.id}`, a));

        const isAnyVisible = itemsToUpdate.some(itemIdent => {
            const item = itemMap.get(`${itemIdent.type}-${itemIdent.id}`);
            return item?.isVisible ?? true;
        });

        const newVisibility = !isAnyVisible;

        const itemIdsToUpdate = {
            token: new Set<number>(),
            annotation: new Set<number>()
        };
        itemsToUpdate.forEach(item => itemIdsToUpdate[item.type].add(item.id));

        setTokens(prev => prev.map(t => itemIdsToUpdate.token.has(t.id) ? { ...t, isVisible: newVisibility } : t));
        setAnnotations(prev => prev.map(a => itemIdsToUpdate.annotation.has(a.id) ? { ...a, isVisible: newVisibility } : a));
    };

  const handleLayersUpdate = (newLayers: LayerEntry[]) => {
      saveStateToHistory();
      setLayers(newLayers);
  };
  
  const handleCustomUnitUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type.startsWith('image/png')) {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomUnits(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };
  
  const handleCustomArtilleryUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    
    Array.from(files).forEach(file => {
        if (file.type.startsWith('image/png')) {
            const reader = new FileReader();
            const filename = file.name;
            const name = filename.substring(0, filename.lastIndexOf('.')) || filename;
            reader.onload = (e) => {
                const imageData = e.target?.result as string;
                if (imageData) {
                    setCustomArtillery(prev => [...prev, { id: getNextId(), name, imageData }]);
                }
            };
            reader.readAsDataURL(file);
        }
    });

    if (event.target) {
        event.target.value = '';
    }
  };

  const handleExportProject = useCallback(() => {
    const projectData: ProjectData = {
      mapImage,
      mapDimensions,
      tokens,
      annotations,
      paletteColors,
      nextId: nextId.current,
      customUnits,
      customArtillery,
      layers,
    };

    const jsonString = JSON.stringify(projectData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'battle-plan.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [mapImage, mapDimensions, tokens, annotations, paletteColors, customUnits, customArtillery, layers]);

  const handleImportProject = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const text = e.target?.result;
        if (typeof text !== 'string') throw new Error('File is not a text file.');
        
        const data: Partial<ProjectData> = JSON.parse(text);

        if (!data || typeof data !== 'object') {
          throw new Error('Invalid project file format.');
        }

        const migratedTokens = (data.tokens || []).map(t => ({
          ...t, isVisible: t.isVisible ?? true, rotation: t.rotation ?? 0,
          animationSpeed: t.animationSpeed ?? 1.0, isPatrol: t.isPatrol ?? false,
        }));
        
        const migratedAnnotations = (data.annotations || []).map(a => ({
          ...a, isVisible: a.isVisible ?? true,
        }));

        setHistory([]);
        setRedoStack([]);
        setTokens(migratedTokens);
        setAnnotations(migratedAnnotations);

        if (data.layers) {
            setLayers(data.layers);
        } else {
            const legacyLayers: LayerEntry[] = [
                ...migratedAnnotations.map(a => ({ id: a.id, type: 'annotation' as const })),
                ...migratedTokens.map(t => ({ id: t.id, type: 'token' as const })),
            ].reverse();
            setLayers(legacyLayers);
        }
        
        setSelectedTokenId(null);
        setDrawingState(null);
        setEditingTokenId(null);
        setActiveTool('select');
        setViewTransform(null);

        setMapImage(data.mapImage ?? null);
        setMapDimensions(data.mapDimensions ?? null);
        setPaletteColors(data.paletteColors ?? INITIAL_PALETTE_COLORS);
        setCustomUnits(data.customUnits ?? []);
        setCustomArtillery(data.customArtillery ?? []);
        nextId.current = data.nextId ?? Math.max(0, ...migratedTokens.map(t => t.id), ...migratedAnnotations.map(a => a.id)) + 1;

        if (event.target) {
            event.target.value = '';
        }
      } catch (error) {
        console.error('Failed to import project:', error);
        alert('Failed to load project file. It might be corrupted or in an invalid format.');
        if (event.target) {
            event.target.value = '';
        }
      }
    };
    reader.readAsText(file);
  };

  const selectedToken = tokens.find(t => t.id === selectedTokenId) ?? null;
  
  const flattenedItemsForCanvas = useMemo(() => {
        const finalItemMap = new Map<string, TokenState | Annotation>();
        tokens.forEach(t => finalItemMap.set(`token-${t.id}`, t));
        annotations.forEach(a => finalItemMap.set(`annotation-${a.id}`, a));

        const flatList: LayerItemIdentifier[] = [];
        const flatten = (items: LayerEntry[]) => {
            for (const item of items) {
                if (item.type === 'group' && !item.isCollapsed) {
                    flatten(item.items);
                } else if (item.type !== 'group') {
                    flatList.push(item);
                }
            }
        };
        flatten(layers);

        return flatList
            .map(identifier => finalItemMap.get(`${identifier.type}-${identifier.id}`))
            .filter((item): item is TokenState | Annotation => !!item);
  }, [layers, tokens, annotations]);

  const [canvasTokens, canvasAnnotations] = useMemo(() => {
        const tkns: TokenState[] = [];
        const anns: Annotation[] = [];
        for (const item of flattenedItemsForCanvas) {
            if (item.type === AnnotationType.Arrow || item.type === AnnotationType.Circle) {
                anns.push(item as Annotation);
            } else {
                tkns.push(item as TokenState);
            }
        }
        return [tkns, anns];
  }, [flattenedItemsForCanvas]);

  return (
    <div className="flex h-screen w-screen bg-gray-900 font-sans">
      <Toolbar 
        activeTool={activeTool}
        onToolSelect={setActiveTool}
        onMapUpload={handleMapUpload}
        onClearAll={clearAll}
        selectedColor={selectedColor}
        onColorChange={setSelectedColor}
        paletteColors={paletteColors}
        onPaletteColorChange={handlePaletteColorChange}
        onUndo={handleUndo}
        onRedo={handleRedo}
        newText={newText}
        onNewTextChange={setNewText}
        textPresets={textPresets}
        onAddTextPreset={handleAddTextPreset}
        onDeleteTextPreset={handleDeleteTextPreset}
        onLoadTextPreset={handleLoadTextPreset}
        selectedToken={selectedToken}
        onTokenUpdate={handleTokenUpdate}
        onClearPath={clearPath}
        onExportProject={handleExportProject}
        onImportProject={handleImportProject}
        customUnits={customUnits}
        onCustomUnitUpload={handleCustomUnitUpload}
        customArtillery={customArtillery}
        onCustomArtilleryUpload={handleCustomArtilleryUpload}
        globalTokenSize={globalTokenSize}
        onGlobalTokenSizeChange={setGlobalTokenSize}
      />
      <main className="flex-1 h-full relative" ref={canvasRef}>
        <Canvas
          mapImage={mapImage}
          mapDimensions={mapDimensions}
          tokens={canvasTokens}
          annotations={canvasAnnotations}
          onDrop={handleTokenDrop}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onWheel={handleWheel}
          drawingState={drawingState}
          activeTool={activeTool}
          selectedColor={selectedColor}
          paletteColors={paletteColors}
          editingTokenId={editingTokenId}
          onTokenUpdate={handleTokenUpdate}
          onFinishEditing={() => setEditingTokenId(null)}
          viewTransform={viewTransform}
          onZoomReset={handleZoomReset}
          globalTokenSize={globalTokenSize}
        />
        <LayersPanel
          layers={layers}
          tokens={tokens}
          annotations={annotations}
          selectedTokenId={selectedTokenId}
          onSelectToken={(id) => {
              setSelectedTokenId(id);
              setActiveTool(t => t === 'select' ? 'path' : t);
          }}
          onToggleVisibility={handleToggleVisibility}
          onToggleGroupVisibility={handleToggleGroupVisibility}
          onDeleteItem={deleteItem}
          onLayersUpdate={handleLayersUpdate}
          getNextId={getNextId}
        />
      </main>
    </div>
  );
};

export default App;
