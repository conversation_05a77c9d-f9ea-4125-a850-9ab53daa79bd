
import React from 'react';

const IconWrapper: React.FC<{ children: React.ReactNode; viewBox?: string }> = ({ children, viewBox = "0 0 24 24" }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox={viewBox} fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" className="w-full h-full">
        {children}
    </svg>
);

export const SwordIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <g transform="rotate(45 50 50)">
            <line x1="50" y1="20" x2="50" y2="80" strokeWidth="8" />
            <line x1="35" y1="65" x2="65" y2="65" strokeWidth="8" />
        </g>
        <g transform="rotate(-45 50 50)">
            <line x1="50" y1="20" x2="50" y2="80" strokeWidth="8" />
            <line x1="35" y1="65" x2="65" y2="65" strokeWidth="8" />
        </g>
    </IconWrapper>
);

export const ShieldIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <path d="M50 15 L85 25 V 60 C 85 80 50 90 50 90 C 50 90 15 80 15 60 V 25 Z" strokeWidth="6" fill="currentColor" />
    </IconWrapper>
);

export const PikeIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <g transform="rotate(30 50 50)">
            <line x1="50" y1="10" x2="50" y2="90" strokeWidth="6" />
            <path d="M45 25 L50 10 L55 25 Z" fill="currentColor" />
        </g>
        <g transform="rotate(-30 50 50)">
            <line x1="50" y1="10" x2="50" y2="90" strokeWidth="6" />
            <path d="M45 25 L50 10 L55 25 Z" fill="currentColor" />
        </g>
    </IconWrapper>
);

export const ArcherIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <path d="M75 20 C 50 40, 50 60, 75 80" strokeWidth="6" />
        <line x1="25" y1="50" x2="85" y2="50" strokeWidth="6" />
        <path d="M85 50 L65 42 V 58 Z" fill="currentColor" />
        <line x1="75" y1="20" x2="75" y2="80" strokeWidth="4" />
    </IconWrapper>
);

export const CavalryIcon: React.FC = () => (
    <IconWrapper viewBox="0 0 100 100">
        <path d="M 40,85 L 60,85 L 60,75 C 70,75 75,70 70,60 L 60,60 C 60,50 70,45 65,30 C 60,15 45,10 40,25 C 35,40 30,50 50,60 L 40,60 Z" fill="currentColor" />
    </IconWrapper>
);

export const TextIcon: React.FC = () => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-full h-full">
        <path fillRule="evenodd" d="M5.25 3a.75.75 0 0 1 .75.75v1.5h8.5V3.75a.75.75 0 0 1 1.5 0v1.5H16.5a.75.75 0 0 1 .75.75v13.5a.75.75 0 0 1-.75-.75H3.75a.75.75 0 0 1-.75-.75V6a.75.75 0 0 1 .75-.75h.5V3.75A.75.75 0 0 1 5.25 3ZM12 15a.75.75 0 0 1-.75-.75v-1.5a.75.75 0 0 1 1.5 0v1.5a.75.75 0 0 1-.75-.75ZM12 6a.75.75 0 0 1 .75.75v3a.75.75 0 0 1-1.5 0v-3A.75.75 0 0 1 12 6Z" clipRule="evenodd" />
    </svg>
);

const GeoIconWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" className="w-full h-full">
        {children}
    </svg>
);

export const CannonIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="40" y="25" width="20" height="50" fill="currentColor" />
    </GeoIconWrapper>
);

export const MetrallaIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="35" y="25" width="30" height="50" fill="currentColor" />
    </GeoIconWrapper>
);

export const MortarIcon: React.FC = () => (
    <GeoIconWrapper>
        <circle cx="50" cy="50" r="25" fill="currentColor" />
    </GeoIconWrapper>
);

export const BallistaIcon: React.FC = () => (
    <GeoIconWrapper>
        <path d="M50 20 L25 70 L75 70 Z" fill="currentColor" />
    </GeoIconWrapper>
);

export const RocketLauncherIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="35" y="20" width="30" height="60" fill="currentColor" />
    </GeoIconWrapper>
);

export const SiegeTowerIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="30" y="30" width="40" height="40" fill="currentColor" />
    </GeoIconWrapper>
);

export const TrebuchetIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="20" y="20" width="60" height="60" fill="currentColor" />
    </GeoIconWrapper>
);

export const CulverinIcon: React.FC = () => (
    <GeoIconWrapper>
        <rect x="45" y="15" width="10" height="70" fill="currentColor" />
    </GeoIconWrapper>
);