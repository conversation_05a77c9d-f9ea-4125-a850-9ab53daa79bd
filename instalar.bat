@echo off
echo ========================================
echo  INSTALADOR - Battle Strategy Creator
echo ========================================
echo.

REM Verificar si Node.js está instalado
echo [1/4] Verificando Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js no está instalado.
    echo.
    echo Por favor, instala Node.js desde: https://nodejs.org/
    echo Descarga la versión LTS (recomendada) y ejecuta el instalador.
    echo.
    echo Después de instalar Node.js, ejecuta este archivo nuevamente.
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Node.js está instalado
    node --version
)

REM Verificar si npm está disponible
echo.
echo [2/4] Verificando npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm no está disponible.
    echo Reinstala Node.js desde: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ npm está disponible
    npm --version
)

REM Instalar dependencias
echo.
echo [3/4] Instalando dependencias del proyecto...
echo Esto puede tomar unos minutos...
npm install
if %errorlevel% neq 0 (
    echo ❌ Error al instalar dependencias.
    echo Verifica tu conexión a internet e intenta nuevamente.
    pause
    exit /b 1
) else (
    echo ✅ Dependencias instaladas correctamente
)

REM Verificar si existe .env.local
echo.
echo [4/4] Verificando configuración...
if not exist ".env.local" (
    echo ⚠️  Archivo .env.local no encontrado.
    echo Creando archivo de configuración...
    echo # Configuración de la aplicación > .env.local
    echo # Agrega tu clave de API de Gemini aquí: >> .env.local
    echo # GEMINI_API_KEY=tu_clave_aqui >> .env.local
    echo.
    echo ✅ Archivo .env.local creado.
    echo 📝 IMPORTANTE: Edita el archivo .env.local y agrega tu GEMINI_API_KEY
) else (
    echo ✅ Archivo .env.local encontrado
)

echo.
echo ========================================
echo  ✅ INSTALACIÓN COMPLETADA
echo ========================================
echo.
echo Para ejecutar el proyecto, usa: ejecutar.bat
echo.
pause
